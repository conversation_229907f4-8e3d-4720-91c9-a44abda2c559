// User Types
export interface User {
  id: number;
  name: string;
  email: string;
  phone?: string;
  user_type: 'super_admin' | 'admin' | 'supervisor' | 'manager' | 'cashier' | 'accountant';
  status: 'active' | 'inactive' | 'suspended';
  avatar?: string;
  company_id?: number;
  branch_id?: number;
  last_login_at?: string;
  company?: Company;
  branch?: Branch;
  permissions: string[];
  roles: string[];
}

// Company Types
export interface Company {
  id: number;
  name: string;
  name_en?: string;
  commercial_register: string;
  tax_number: string;
  email: string;
  phone: string;
  address: string;
  address_en?: string;
  city: string;
  country: string;
  logo?: string;
  status: 'active' | 'inactive' | 'suspended';
  subscription_price: number;
  max_branches: number;
  current_branches: number;
  trial_ends_at?: string;
  subscription_ends_at?: string;
  created_at: string;
  updated_at: string;
}

// Branch Types
export interface Branch {
  id: number;
  company_id: number;
  name: string;
  name_en?: string;
  code: string;
  phone?: string;
  address: string;
  address_en?: string;
  city: string;
  latitude?: number;
  longitude?: number;
  status: 'active' | 'inactive';
  is_main: boolean;
  created_at: string;
  updated_at: string;
}

// Product Types
export interface Product {
  id: number;
  company_id: number;
  category_id: number;
  name: string;
  name_en?: string;
  description?: string;
  description_en?: string;
  sku: string;
  barcode?: string;
  image?: string;
  images?: string[];
  cost_price: number;
  selling_price: number;
  discount_price?: number;
  tax_type: 'inclusive' | 'exclusive' | 'none';
  tax_rate: number;
  unit: string;
  track_quantity: boolean;
  quantity: number;
  min_quantity: number;
  max_quantity?: number;
  expiry_date?: string;
  is_active: boolean;
  is_featured: boolean;
  attributes?: Record<string, any>;
  category?: Category;
  stock_status?: 'in_stock' | 'low_stock' | 'out_of_stock' | 'unlimited';
  created_at: string;
  updated_at: string;
}

// Category Types
export interface Category {
  id: number;
  company_id: number;
  name: string;
  name_en?: string;
  description?: string;
  description_en?: string;
  image?: string;
  color: string;
  sort_order: number;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

// Customer Types
export interface Customer {
  id: number;
  company_id: number;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  tax_number?: string;
  type: 'individual' | 'company';
  credit_limit: number;
  current_balance: number;
  birth_date?: string;
  gender?: 'male' | 'female';
  is_active: boolean;
  last_purchase_at?: string;
  created_at: string;
  updated_at: string;
}

// Sale Types
export interface Sale {
  id: number;
  company_id: number;
  branch_id: number;
  user_id: number;
  customer_id?: number;
  invoice_number: string;
  type: 'sale' | 'return' | 'exchange';
  status: 'pending' | 'completed' | 'cancelled' | 'refunded';
  subtotal: number;
  discount_amount: number;
  discount_percentage: number;
  tax_amount: number;
  total_amount: number;
  paid_amount: number;
  change_amount: number;
  payment_method: 'cash' | 'card' | 'transfer' | 'credit' | 'mixed';
  payment_details?: Record<string, any>;
  notes?: string;
  is_printed: boolean;
  sale_date: string;
  user?: User;
  customer?: Customer;
  branch?: Branch;
  items?: SaleItem[];
  created_at: string;
  updated_at: string;
}

// Sale Item Types
export interface SaleItem {
  id: number;
  sale_id: number;
  product_id: number;
  product_name: string;
  product_sku: string;
  quantity: number;
  unit_price: number;
  discount_amount: number;
  tax_rate: number;
  tax_amount: number;
  total_amount: number;
  product?: Product;
  created_at: string;
  updated_at: string;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  errors?: Record<string, string[]>;
}

export interface PaginatedResponse<T = any> {
  success: boolean;
  data: {
    [key: string]: T[];
    pagination: {
      current_page: number;
      last_page: number;
      per_page: number;
      total: number;
    };
  };
}

// Auth Types
export interface LoginCredentials {
  email: string;
  password: string;
}

export interface AuthResponse {
  user: User;
  token: string;
  token_type: string;
  expires_in: number;
}

// Form Types
export interface ProductFormData {
  name: string;
  name_en?: string;
  category_id: number;
  description?: string;
  description_en?: string;
  sku: string;
  barcode?: string;
  cost_price: number;
  selling_price: number;
  discount_price?: number;
  tax_type: 'inclusive' | 'exclusive' | 'none';
  tax_rate: number;
  unit: string;
  track_quantity: boolean;
  quantity?: number;
  min_quantity?: number;
  max_quantity?: number;
  expiry_date?: string;
  is_featured: boolean;
  image?: File;
}

// POS Types
export interface CartItem {
  product: Product;
  quantity: number;
  unit_price: number;
  total: number;
}

export interface Cart {
  items: CartItem[];
  subtotal: number;
  discount_amount: number;
  discount_percentage: number;
  tax_amount: number;
  total: number;
}

// Dashboard Stats Types
export interface DashboardStats {
  period: string;
  total_sales: number;
  total_revenue: number;
  total_profit: number;
  average_sale_value: number;
  cash_sales: number;
  card_sales: number;
  top_selling_products: any[];
  hourly_sales: any[];
}

// Theme Types
export interface ThemeConfig {
  mode: 'light' | 'dark';
  primaryColor: string;
  language: 'ar' | 'en';
  direction: 'rtl' | 'ltr';
}

// Notification Types
export interface Notification {
  id: number;
  type: string;
  title: string;
  message: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  is_read: boolean;
  created_at: string;
}
