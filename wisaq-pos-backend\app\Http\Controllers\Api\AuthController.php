<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use <PERSON><PERSON>\JWTAuth\Facades\JWTAuth;
use Tymon\JWTAuth\Exceptions\JWTException;

class AuthController extends Controller
{
    /**
     * تسجيل الدخول
     */
    public function login(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'email' => 'required|email',
                'password' => 'required|string|min:6',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صحيحة',
                    'errors' => $validator->errors()
                ], 422);
            }

            $credentials = $request->only('email', 'password');

            // محاولة تسجيل الدخول
            if (!$token = JWTAuth::attempt($credentials)) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات الدخول غير صحيحة'
                ], 401);
            }

            $user = Auth::user();

            // التحقق من حالة المستخدم
            if (!$user->isActive()) {
                return response()->json([
                    'success' => false,
                    'message' => 'حسابك غير نشط، يرجى التواصل مع الإدارة'
                ], 403);
            }

            // التحقق من حالة الشركة (إذا لم يكن مدير عام)
            if (!$user->isSuperAdmin() && $user->company) {
                if ($user->company->status !== 'active') {
                    return response()->json([
                        'success' => false,
                        'message' => 'حساب الشركة غير نشط'
                    ], 403);
                }

                // التحقق من انتهاء الاشتراك
                if ($user->company->subscriptionExpired()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'انتهت صلاحية اشتراك الشركة'
                    ], 403);
                }
            }

            // تحديث آخر تسجيل دخول
            $user->updateLastLogin($request->ip());

            return response()->json([
                'success' => true,
                'message' => 'تم تسجيل الدخول بنجاح',
                'data' => [
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'phone' => $user->phone,
                        'user_type' => $user->user_type,
                        'avatar' => $user->avatar,
                        'company_id' => $user->company_id,
                        'branch_id' => $user->branch_id,
                        'company' => $user->company ? [
                            'id' => $user->company->id,
                            'name' => $user->company->name,
                            'logo' => $user->company->logo,
                        ] : null,
                        'branch' => $user->branch ? [
                            'id' => $user->branch->id,
                            'name' => $user->branch->name,
                        ] : null,
                        'permissions' => $user->getAllPermissions()->pluck('name'),
                        'roles' => $user->getRoleNames(),
                    ],
                    'token' => $token,
                    'token_type' => 'bearer',
                    'expires_in' => JWTAuth::factory()->getTTL() * 60
                ]
            ]);

        } catch (JWTException $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في إنشاء الرمز المميز'
            ], 500);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ غير متوقع'
            ], 500);
        }
    }

    /**
     * تسجيل الخروج
     */
    public function logout(): JsonResponse
    {
        try {
            JWTAuth::invalidate(JWTAuth::getToken());

            return response()->json([
                'success' => true,
                'message' => 'تم تسجيل الخروج بنجاح'
            ]);
        } catch (JWTException $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في تسجيل الخروج'
            ], 500);
        }
    }

    /**
     * تحديث الرمز المميز
     */
    public function refresh(): JsonResponse
    {
        try {
            $token = JWTAuth::refresh(JWTAuth::getToken());

            return response()->json([
                'success' => true,
                'data' => [
                    'token' => $token,
                    'token_type' => 'bearer',
                    'expires_in' => JWTAuth::factory()->getTTL() * 60
                ]
            ]);
        } catch (JWTException $e) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن تحديث الرمز المميز'
            ], 401);
        }
    }

    /**
     * الحصول على بيانات المستخدم الحالي
     */
    public function me(): JsonResponse
    {
        try {
            $user = Auth::user();

            return response()->json([
                'success' => true,
                'data' => [
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'phone' => $user->phone,
                        'user_type' => $user->user_type,
                        'avatar' => $user->avatar,
                        'company_id' => $user->company_id,
                        'branch_id' => $user->branch_id,
                        'last_login_at' => $user->last_login_at,
                        'company' => $user->company ? [
                            'id' => $user->company->id,
                            'name' => $user->company->name,
                            'logo' => $user->company->logo,
                            'status' => $user->company->status,
                            'subscription_ends_at' => $user->company->subscription_ends_at,
                        ] : null,
                        'branch' => $user->branch ? [
                            'id' => $user->branch->id,
                            'name' => $user->branch->name,
                            'code' => $user->branch->code,
                        ] : null,
                        'permissions' => $user->getAllPermissions()->pluck('name'),
                        'roles' => $user->getRoleNames(),
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في الحصول على بيانات المستخدم'
            ], 500);
        }
    }
}
