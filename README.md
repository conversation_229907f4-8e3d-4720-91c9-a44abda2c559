# نظام نقاط البيع Wisaq POS

نظام نقاط بيع متكامل وحديث مع نظام اشتراكات (SaaS) مخصص للمحلات والمتاجر والشركات.

## 🚀 الميزات الرئيسية

### 📊 لوحة إدارة رئيسية (Admin Panel)
- إدارة الاشتراكات والمستخدمين والشركات والمتاجر والفروع
- إعداد باقات الاشتراك والأسعار
- إدارة الدفع الآلي وتقارير الدخل
- إعدادات عامة للنظام
- إدارة صلاحيات الإشراف

### 🏢 لوحة تحكم الشركات (Client Panel)
- نظام نقاط بيع POS سريع ومباشر
- دعم أجهزة قراءة الباركود والبحث الذكي
- إدارة المخزون مع التنبيهات
- إدارة الفروع المتعددة
- إنشاء حسابات متعددة بصلاحيات محددة
- طباعة الفواتير الضريبية
- تقارير المبيعات المفصلة
- نظام الضرائب والخصومات والعروض

### 💳 نظام الدفع والاشتراكات
- تكامل كامل مع Tap Payment
- اشتراكات شهرية آلية
- نظام تسعير متدرج للفروع الإضافية
- إدارة الفواتير والمدفوعات

## 🛠️ التقنيات المستخدمة

### Backend
- **Laravel 10** - إطار العمل الرئيسي
- **MySQL 8.0** - قاعدة البيانات
- **JWT Authentication** - نظام المصادقة
- **Spatie Laravel Permission** - إدارة الصلاحيات
- **Redis** - التخزين المؤقت والجلسات

### Frontend
- **React 18** - مكتبة واجهة المستخدم
- **TypeScript** - للكود الآمن
- **Vite** - أداة البناء السريعة
- **Tailwind CSS** - إطار التصميم
- **React Query** - إدارة البيانات
- **i18next** - الترجمة ودعم RTL

## 📋 متطلبات النظام

- PHP 8.1 أو أحدث
- Node.js 18 أو أحدث
- MySQL 8.0 أو أحدث
- Redis Server
- Composer
- NPM أو Yarn

## 🔧 التثبيت والإعداد

### 1. إعداد Backend (Laravel)

```bash
# الانتقال لمجلد Backend
cd wisaq-pos-backend

# تثبيت المكتبات
composer install

# نسخ ملف البيئة
cp .env.example .env

# إنشاء مفتاح التطبيق
php artisan key:generate

# إنشاء مفتاح JWT
php artisan jwt:secret
```

### 2. إعداد قاعدة البيانات

```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE wisaq_pos CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 3. تحديث ملف .env

```env
# معلومات التطبيق
APP_NAME="Wisaq POS System"
APP_URL=http://localhost:8000
APP_LOCALE=ar

# قاعدة البيانات
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=wisaq_pos
DB_USERNAME=root
DB_PASSWORD=

# Redis
CACHE_STORE=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

# Tap Payment
TAP_SECRET_KEY=your-tap-secret-key-here
TAP_PUBLIC_KEY=your-tap-public-key-here
TAP_WEBHOOK_SECRET=your-tap-webhook-secret-here
TAP_ENVIRONMENT=sandbox

# إعدادات النظام
SYSTEM_TIMEZONE=Asia/Riyadh
SYSTEM_CURRENCY=SAR
SYSTEM_TAX_RATE=15

# إعدادات الاشتراك
SUBSCRIPTION_BASE_PRICE=99.00
SUBSCRIPTION_ADDITIONAL_BRANCH_RATE=30
SUBSCRIPTION_BRANCHES_PER_TIER=5
```

### 4. تشغيل المايجريشن والسيدر

```bash
# تشغيل المايجريشن
php artisan migrate

# تشغيل السيدر لإنشاء الأدوار والصلاحيات
php artisan db:seed
```

### 5. إعداد Frontend (React)

```bash
# الانتقال لمجلد Frontend
cd ../wisaq-pos-frontend

# تثبيت المكتبات
npm install

# تشغيل الخادم التطويري
npm run dev
```

### 6. تشغيل النظام

#### الطريقة السريعة (Windows):
```bash
# تشغيل النظام كاملاً بنقرة واحدة
start-wisaq-pos.bat
```

#### الطريقة اليدوية:

**تشغيل Backend:**
```bash
cd wisaq-pos-backend
php artisan serve --host=0.0.0.0 --port=8000
```

**تشغيل Frontend:**
```bash
cd wisaq-pos-frontend
npm run dev
```

## 🔐 بيانات الدخول الافتراضية

**المدير العام:**
- البريد الإلكتروني: `<EMAIL>`
- كلمة المرور: `password`

## 🌐 روابط النظام

بعد تشغيل النظام، يمكنك الوصول إلى:

- **Frontend (واجهة المستخدم):** http://localhost:5173
- **Backend API:** http://localhost:8000/api
- **Laravel Admin:** http://localhost:8000

## 📚 هيكل قاعدة البيانات

### الجداول الرئيسية:
- `companies` - بيانات الشركات
- `branches` - فروع الشركات
- `users` - المستخدمين
- `products` - المنتجات
- `categories` - فئات المنتجات
- `customers` - العملاء
- `suppliers` - الموردين
- `sales` - المبيعات
- `sale_items` - بنود المبيعات
- `inventory_logs` - سجلات المخزون
- `subscriptions` - الاشتراكات
- `payments` - المدفوعات
- `notifications` - التنبيهات
- `settings` - الإعدادات

## 🔌 API Endpoints

### المصادقة
- `POST /api/auth/login` - تسجيل الدخول
- `POST /api/auth/logout` - تسجيل الخروج
- `POST /api/auth/refresh` - تحديث الرمز المميز
- `GET /api/auth/me` - بيانات المستخدم الحالي

### إدارة الشركات (Super Admin)
- `GET /api/admin/companies` - قائمة الشركات
- `POST /api/admin/companies` - إنشاء شركة جديدة
- `PUT /api/admin/companies/{id}` - تحديث بيانات الشركة
- `DELETE /api/admin/companies/{id}` - حذف الشركة

### إدارة المنتجات
- `GET /api/products` - قائمة المنتجات
- `POST /api/products` - إضافة منتج جديد
- `PUT /api/products/{id}` - تحديث المنتج
- `DELETE /api/products/{id}` - حذف المنتج
- `GET /api/products/search/{query}` - البحث في المنتجات
- `GET /api/products/barcode/{barcode}` - البحث بالباركود

### نقاط البيع (POS)
- `POST /api/pos/create-sale` - إنشاء عملية بيع جديدة
- `POST /api/pos/add-item` - إضافة منتج للبيع
- `POST /api/pos/process-payment` - معالجة الدفع

## 🔒 الأمان

- تشفير كلمات المرور باستخدام Bcrypt
- حماية API باستخدام JWT
- حماية من CSRF, XSS, SQL Injection
- تسجيل نشاط المستخدمين (Audit Log)
- تشفير البيانات الحساسة

## 📱 دعم الأجهزة

- متجاوب مع جميع أحجام الشاشات
- دعم الأجهزة اللوحية والهواتف الذكية
- دعم قراءة الباركود عبر الكاميرا
- دعم الطابعات الحرارية

## 🌐 دعم اللغات

- العربية (افتراضي)
- الإنجليزية
- دعم RTL كامل
- ترجمة الواجهات والتقارير

## 📞 الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966 XX XXX XXXX

## 📄 الترخيص

هذا المشروع محمي بحقوق الطبع والنشر © 2025 Wisaq Systems. جميع الحقوق محفوظة.
