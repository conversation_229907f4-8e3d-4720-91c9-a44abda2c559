<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Sale extends Model
{
    use SoftDeletes;

    /**
     * الحقول القابلة للتعبئة
     */
    protected $fillable = [
        'company_id',
        'branch_id',
        'user_id',
        'customer_id',
        'invoice_number',
        'type',
        'status',
        'subtotal',
        'discount_amount',
        'discount_percentage',
        'tax_amount',
        'total_amount',
        'paid_amount',
        'change_amount',
        'payment_method',
        'payment_details',
        'notes',
        'is_printed',
        'sale_date',
    ];

    /**
     * تحويل الحقول
     */
    protected $casts = [
        'subtotal' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'discount_percentage' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'change_amount' => 'decimal:2',
        'payment_details' => 'array',
        'is_printed' => 'boolean',
        'sale_date' => 'datetime',
    ];

    /**
     * العلاقات
     */

    /**
     * الشركة التابعة لها المبيعة
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * الفرع التابع له المبيعة
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * المستخدم (الكاشير) الذي قام بالبيع
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * العميل
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * بنود المبيعة
     */
    public function items(): HasMany
    {
        return $this->hasMany(SaleItem::class);
    }

    /**
     * Scopes
     */

    /**
     * المبيعات المكتملة
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * المبيعات في شركة معينة
     */
    public function scopeInCompany($query, $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    /**
     * المبيعات في فرع معين
     */
    public function scopeInBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * المبيعات في فترة معينة
     */
    public function scopeBetweenDates($query, $startDate, $endDate)
    {
        return $query->whereBetween('sale_date', [$startDate, $endDate]);
    }

    /**
     * مبيعات اليوم
     */
    public function scopeToday($query)
    {
        return $query->whereDate('sale_date', today());
    }

    /**
     * مبيعات هذا الشهر
     */
    public function scopeThisMonth($query)
    {
        return $query->whereMonth('sale_date', now()->month)
                    ->whereYear('sale_date', now()->year);
    }

    /**
     * مبيعات هذا العام
     */
    public function scopeThisYear($query)
    {
        return $query->whereYear('sale_date', now()->year);
    }

    /**
     * Helper Methods
     */

    /**
     * هل المبيعة مكتملة؟
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * هل المبيعة مرتجعة؟
     */
    public function isRefunded(): bool
    {
        return $this->status === 'refunded';
    }

    /**
     * هل المبيعة ملغاة؟
     */
    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    /**
     * هل تم طباعة الفاتورة؟
     */
    public function isPrinted(): bool
    {
        return $this->is_printed;
    }

    /**
     * الحصول على المبلغ المتبقي
     */
    public function getRemainingAmountAttribute(): float
    {
        return max(0, $this->total_amount - $this->paid_amount);
    }

    /**
     * هل المبيعة مدفوعة بالكامل؟
     */
    public function isFullyPaid(): bool
    {
        return $this->paid_amount >= $this->total_amount;
    }

    /**
     * تحديد حالة المبيعة كمطبوعة
     */
    public function markAsPrinted(): void
    {
        $this->update(['is_printed' => true]);
    }

    /**
     * إنشاء رقم فاتورة جديد
     */
    public static function generateInvoiceNumber($companyId, $branchId): string
    {
        $prefix = 'INV';
        $date = now()->format('Ymd');
        $branchCode = str_pad($branchId, 2, '0', STR_PAD_LEFT);

        // الحصول على آخر رقم فاتورة لهذا اليوم
        $lastSale = static::where('company_id', $companyId)
            ->where('branch_id', $branchId)
            ->whereDate('sale_date', today())
            ->orderBy('id', 'desc')
            ->first();

        $sequence = 1;
        if ($lastSale) {
            // استخراج الرقم التسلسلي من آخر فاتورة
            $lastNumber = substr($lastSale->invoice_number, -4);
            $sequence = intval($lastNumber) + 1;
        }

        $sequenceStr = str_pad($sequence, 4, '0', STR_PAD_LEFT);

        return "{$prefix}-{$date}-{$branchCode}-{$sequenceStr}";
    }

    /**
     * حساب الإجماليات
     */
    public function calculateTotals(): void
    {
        $subtotal = $this->items->sum(function ($item) {
            return $item->quantity * $item->unit_price;
        });

        $discountAmount = $this->discount_percentage > 0
            ? ($subtotal * $this->discount_percentage / 100)
            : $this->discount_amount;

        $subtotalAfterDiscount = $subtotal - $discountAmount;

        $taxAmount = $this->items->sum('tax_amount');

        $total = $subtotalAfterDiscount + $taxAmount;

        $this->update([
            'subtotal' => $subtotal,
            'discount_amount' => $discountAmount,
            'tax_amount' => $taxAmount,
            'total_amount' => $total,
        ]);
    }

    /**
     * إضافة بند للمبيعة
     */
    public function addItem(Product $product, int $quantity, ?float $unitPrice = null): SaleItem
    {
        $unitPrice = $unitPrice ?? $product->final_price;

        $item = $this->items()->create([
            'product_id' => $product->id,
            'product_name' => $product->name,
            'product_sku' => $product->sku,
            'quantity' => $quantity,
            'unit_price' => $unitPrice,
            'tax_rate' => $product->tax_rate,
            'tax_amount' => $product->tax_type === 'inclusive'
                ? ($unitPrice * $quantity) - (($unitPrice * $quantity) / (1 + ($product->tax_rate / 100)))
                : ($unitPrice * $quantity) * ($product->tax_rate / 100),
            'total_amount' => $unitPrice * $quantity,
        ]);

        // تحديث كمية المنتج
        $product->updateQuantity(-$quantity, 'sale', $this);

        // إعادة حساب الإجماليات
        $this->calculateTotals();

        return $item;
    }
}
