<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Branch extends Model
{
    use SoftDeletes;

    /**
     * الحقول القابلة للتعبئة
     */
    protected $fillable = [
        'company_id',
        'name',
        'name_en',
        'code',
        'phone',
        'address',
        'address_en',
        'city',
        'latitude',
        'longitude',
        'status',
        'settings',
        'is_main',
    ];

    /**
     * تحويل الحقول
     */
    protected $casts = [
        'settings' => 'array',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'is_main' => 'boolean',
    ];

    /**
     * العلاقات
     */

    /**
     * الشركة التابع لها الفرع
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * المستخدمين في الفرع
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    /**
     * المبيعات في الفرع
     */
    public function sales(): HasMany
    {
        return $this->hasMany(Sale::class);
    }

    /**
     * سجلات المخزون في الفرع
     */
    public function inventoryLogs(): HasMany
    {
        return $this->hasMany(InventoryLog::class);
    }

    /**
     * Scopes
     */

    /**
     * الفروع النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * الفروع في شركة معينة
     */
    public function scopeInCompany($query, $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    /**
     * الفرع الرئيسي
     */
    public function scopeMain($query)
    {
        return $query->where('is_main', true);
    }

    /**
     * Helper Methods
     */

    /**
     * هل الفرع نشط؟
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * هل الفرع رئيسي؟
     */
    public function isMain(): bool
    {
        return $this->is_main;
    }

    /**
     * الحصول على العنوان الكامل
     */
    public function getFullAddressAttribute(): string
    {
        return $this->address . ', ' . $this->city;
    }

    /**
     * الحصول على إحداثيات الموقع
     */
    public function getLocationAttribute(): ?array
    {
        if ($this->latitude && $this->longitude) {
            return [
                'lat' => (float) $this->latitude,
                'lng' => (float) $this->longitude,
            ];
        }

        return null;
    }

    /**
     * تعيين الفرع كرئيسي
     */
    public function setAsMain(): void
    {
        // إلغاء تعيين الفروع الأخرى كرئيسية
        $this->company->branches()->update(['is_main' => false]);

        // تعيين هذا الفرع كرئيسي
        $this->update(['is_main' => true]);
    }

    /**
     * عدد المبيعات اليوم
     */
    public function getTodaySalesCountAttribute(): int
    {
        return $this->sales()
            ->whereDate('sale_date', today())
            ->where('status', 'completed')
            ->count();
    }

    /**
     * إجمالي مبيعات اليوم
     */
    public function getTodaySalesTotalAttribute(): float
    {
        return $this->sales()
            ->whereDate('sale_date', today())
            ->where('status', 'completed')
            ->sum('total_amount');
    }

    /**
     * عدد المستخدمين النشطين
     */
    public function getActiveUsersCountAttribute(): int
    {
        return $this->users()->where('status', 'active')->count();
    }
}
