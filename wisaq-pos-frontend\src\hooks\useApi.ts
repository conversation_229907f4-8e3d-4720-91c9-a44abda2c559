import { useState, useEffect, useCallback } from 'react';
import { apiService } from '../services/api';
import { ApiResponse, PaginatedResponse } from '../types';
import toast from 'react-hot-toast';

interface UseApiOptions {
  immediate?: boolean;
  showErrorToast?: boolean;
  showSuccessToast?: boolean;
}

interface UseApiState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

export function useApi<T = any>(
  apiCall: () => Promise<ApiResponse<T>>,
  options: UseApiOptions = {}
) {
  const { immediate = false, showErrorToast = true, showSuccessToast = false } = options;
  
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    loading: false,
    error: null,
  });

  const execute = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const response = await apiCall();
      
      if (response.success) {
        setState({
          data: response.data || null,
          loading: false,
          error: null,
        });
        
        if (showSuccessToast && response.message) {
          toast.success(response.message);
        }
        
        return response.data;
      } else {
        const errorMessage = response.message || 'حدث خطأ غير متوقع';
        setState({
          data: null,
          loading: false,
          error: errorMessage,
        });
        
        if (showErrorToast) {
          toast.error(errorMessage);
        }
        
        throw new Error(errorMessage);
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message || 'حدث خطأ في الاتصال';
      
      setState({
        data: null,
        loading: false,
        error: errorMessage,
      });
      
      if (showErrorToast) {
        toast.error(errorMessage);
      }
      
      throw error;
    }
  }, [apiCall, showErrorToast, showSuccessToast]);

  useEffect(() => {
    if (immediate) {
      execute();
    }
  }, [immediate, execute]);

  return {
    ...state,
    execute,
    refetch: execute,
  };
}

// Hook for paginated data
export function usePaginatedApi<T = any>(
  apiCall: (params?: any) => Promise<PaginatedResponse<T>>,
  initialParams: any = {},
  options: UseApiOptions = {}
) {
  const [params, setParams] = useState(initialParams);
  const [allData, setAllData] = useState<T[]>([]);
  const [pagination, setPagination] = useState({
    current_page: 1,
    last_page: 1,
    per_page: 15,
    total: 0,
  });

  const { data, loading, error, execute } = useApi(
    () => apiCall(params),
    { ...options, immediate: false }
  );

  useEffect(() => {
    execute();
  }, [params, execute]);

  useEffect(() => {
    if (data) {
      const key = Object.keys(data).find(k => k !== 'pagination');
      if (key && data[key]) {
        if (params.page === 1) {
          setAllData(data[key]);
        } else {
          setAllData(prev => [...prev, ...data[key]]);
        }
        setPagination(data.pagination);
      }
    }
  }, [data, params.page]);

  const loadMore = useCallback(() => {
    if (pagination.current_page < pagination.last_page && !loading) {
      setParams(prev => ({ ...prev, page: prev.page + 1 }));
    }
  }, [pagination, loading]);

  const refresh = useCallback(() => {
    setParams(prev => ({ ...prev, page: 1 }));
    setAllData([]);
  }, []);

  const updateParams = useCallback((newParams: any) => {
    setParams(prev => ({ ...prev, ...newParams, page: 1 }));
    setAllData([]);
  }, []);

  return {
    data: allData,
    pagination,
    loading,
    error,
    loadMore,
    refresh,
    updateParams,
    hasMore: pagination.current_page < pagination.last_page,
  };
}

// Hook for mutations (create, update, delete)
export function useMutation<T = any, P = any>(
  mutationFn: (params: P) => Promise<ApiResponse<T>>,
  options: UseApiOptions & {
    onSuccess?: (data: T) => void;
    onError?: (error: string) => void;
  } = {}
) {
  const { showErrorToast = true, showSuccessToast = true, onSuccess, onError } = options;
  
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    loading: false,
    error: null,
  });

  const mutate = useCallback(async (params: P) => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const response = await mutationFn(params);
      
      if (response.success) {
        setState({
          data: response.data || null,
          loading: false,
          error: null,
        });
        
        if (showSuccessToast && response.message) {
          toast.success(response.message);
        }
        
        if (onSuccess && response.data) {
          onSuccess(response.data);
        }
        
        return response.data;
      } else {
        const errorMessage = response.message || 'حدث خطأ غير متوقع';
        setState({
          data: null,
          loading: false,
          error: errorMessage,
        });
        
        if (showErrorToast) {
          toast.error(errorMessage);
        }
        
        if (onError) {
          onError(errorMessage);
        }
        
        throw new Error(errorMessage);
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message || 'حدث خطأ في الاتصال';
      
      setState({
        data: null,
        loading: false,
        error: errorMessage,
      });
      
      if (showErrorToast) {
        toast.error(errorMessage);
      }
      
      if (onError) {
        onError(errorMessage);
      }
      
      throw error;
    }
  }, [mutationFn, showErrorToast, showSuccessToast, onSuccess, onError]);

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
    });
  }, []);

  return {
    ...state,
    mutate,
    reset,
  };
}

// Specific hooks for common operations
export const useProducts = (params?: any) => {
  return usePaginatedApi(apiService.getProducts.bind(apiService), { page: 1, per_page: 15, ...params });
};

export const useCategories = (params?: any) => {
  return useApi(() => apiService.getCategories(params), { immediate: true });
};

export const useSales = (params?: any) => {
  return usePaginatedApi(apiService.getSales.bind(apiService), { page: 1, per_page: 15, ...params });
};

export const useCustomers = (params?: any) => {
  return usePaginatedApi(apiService.getCustomers.bind(apiService), { page: 1, per_page: 15, ...params });
};

export const useDashboardStats = (params?: any) => {
  return useApi(() => apiService.getDashboardStats(params), { immediate: true });
};
