<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Company;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class CompanyController extends Controller
{
    /**
     * عرض قائمة الشركات (Super Admin فقط)
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $perPage = $request->get('per_page', 15);

            $query = Company::with(['branches', 'users', 'activeSubscription']);

            // فلترة حسب الحالة
            if ($request->has('status') && $request->status) {
                if ($request->status === 'active') {
                    $query->active();
                } elseif ($request->status === 'suspended') {
                    $query->suspended();
                } elseif ($request->status === 'trial_expired') {
                    $query->trialExpired();
                } elseif ($request->status === 'subscription_expired') {
                    $query->subscriptionExpired();
                }
            }

            // البحث
            if ($request->has('search') && $request->search) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%")
                      ->orWhere('commercial_register', 'like', "%{$search}%")
                      ->orWhere('tax_number', 'like', "%{$search}%");
                });
            }

            // الترتيب
            $sortBy = $request->get('sort_by', 'created_at');
            $sortOrder = $request->get('sort_order', 'desc');
            $query->orderBy($sortBy, $sortOrder);

            $companies = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => [
                    'companies' => $companies->items(),
                    'pagination' => [
                        'current_page' => $companies->currentPage(),
                        'last_page' => $companies->lastPage(),
                        'per_page' => $companies->perPage(),
                        'total' => $companies->total(),
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في جلب الشركات'
            ], 500);
        }
    }

    /**
     * إنشاء شركة جديدة
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'name_en' => 'nullable|string|max:255',
                'commercial_register' => 'required|string|unique:companies,commercial_register',
                'tax_number' => 'required|string|unique:companies,tax_number',
                'email' => 'required|email|unique:companies,email',
                'phone' => 'required|string|max:20',
                'address' => 'required|string',
                'address_en' => 'nullable|string',
                'city' => 'required|string|max:100',
                'country' => 'nullable|string|max:100',
                'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
                'subscription_price' => 'nullable|numeric|min:0',
                'max_branches' => 'nullable|integer|min:1',
                'trial_days' => 'nullable|integer|min:0|max:365',

                // بيانات المدير
                'admin_name' => 'required|string|max:255',
                'admin_email' => 'required|email|unique:users,email',
                'admin_phone' => 'nullable|string|max:20',
                'admin_password' => 'required|string|min:6',

                // بيانات الفرع الرئيسي
                'branch_name' => 'required|string|max:255',
                'branch_address' => 'required|string',
                'branch_city' => 'required|string|max:100',
                'branch_phone' => 'nullable|string|max:20',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صحيحة',
                    'errors' => $validator->errors()
                ], 422);
            }

            DB::beginTransaction();

            try {
                $data = $request->except(['logo', 'admin_name', 'admin_email', 'admin_phone', 'admin_password', 'branch_name', 'branch_address', 'branch_city', 'branch_phone', 'trial_days']);

                // إعداد القيم الافتراضية
                $data['subscription_price'] = $request->subscription_price ?? 99.00;
                $data['max_branches'] = $request->max_branches ?? 1;
                $data['current_branches'] = 1;
                $data['status'] = 'active';

                // إعداد فترة التجربة
                $trialDays = $request->trial_days ?? 30;
                $data['trial_ends_at'] = now()->addDays($trialDays);
                $data['subscription_ends_at'] = now()->addDays($trialDays);

                // رفع الشعار
                if ($request->hasFile('logo')) {
                    $logo = $request->file('logo');
                    $logoName = time() . '_' . Str::random(10) . '.' . $logo->getClientOriginalExtension();
                    $logoPath = $logo->storeAs('companies/logos', $logoName, 'public');
                    $data['logo'] = $logoPath;
                }

                // إنشاء الشركة
                $company = Company::create($data);

                // إنشاء الفرع الرئيسي
                $branch = $company->branches()->create([
                    'name' => $request->branch_name,
                    'name_en' => $request->branch_name,
                    'code' => 'MAIN-001',
                    'phone' => $request->branch_phone,
                    'address' => $request->branch_address,
                    'address_en' => $request->branch_address,
                    'city' => $request->branch_city,
                    'status' => 'active',
                    'is_main' => true,
                ]);

                // إنشاء مدير الشركة
                $admin = User::create([
                    'name' => $request->admin_name,
                    'email' => $request->admin_email,
                    'phone' => $request->admin_phone,
                    'password' => Hash::make($request->admin_password),
                    'company_id' => $company->id,
                    'branch_id' => $branch->id,
                    'user_type' => 'admin',
                    'status' => 'active',
                ]);

                // تعيين دور المدير
                $admin->assignRole('admin');

                // إنشاء اشتراك أولي
                $subscription = $company->subscriptions()->create([
                    'plan_name' => 'الخطة الأساسية',
                    'base_price' => $company->subscription_price,
                    'included_branches' => 1,
                    'additional_branches' => 0,
                    'additional_branch_price' => 0,
                    'total_price' => $company->subscription_price,
                    'billing_cycle' => 'monthly',
                    'status' => 'active',
                    'start_date' => now(),
                    'end_date' => $company->subscription_ends_at,
                    'next_billing_date' => $company->subscription_ends_at,
                    'auto_renew' => true,
                    'features' => [
                        'branches' => 1,
                        'users' => 'unlimited',
                        'products' => 'unlimited',
                        'pos' => true,
                        'reports' => true,
                        'inventory' => true,
                    ],
                ]);

                DB::commit();

                return response()->json([
                    'success' => true,
                    'message' => 'تم إنشاء الشركة بنجاح',
                    'data' => [
                        'company' => $company->load(['branches', 'users', 'activeSubscription']),
                        'admin_credentials' => [
                            'email' => $admin->email,
                            'password' => 'تم إرسال كلمة المرور عبر البريد الإلكتروني'
                        ]
                    ]
                ], 201);

            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في إنشاء الشركة: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * عرض شركة محددة
     */
    public function show($id): JsonResponse
    {
        try {
            $company = Company::with([
                'branches',
                'users' => function($query) {
                    $query->select('id', 'name', 'email', 'user_type', 'status', 'company_id', 'branch_id', 'last_login_at');
                },
                'activeSubscription',
                'subscriptions' => function($query) {
                    $query->latest()->limit(5);
                }
            ])->find($id);

            if (!$company) {
                return response()->json([
                    'success' => false,
                    'message' => 'الشركة غير موجودة'
                ], 404);
            }

            // إحصائيات إضافية
            $stats = [
                'total_users' => $company->users->count(),
                'active_users' => $company->users->where('status', 'active')->count(),
                'total_branches' => $company->branches->count(),
                'active_branches' => $company->branches->where('status', 'active')->count(),
                'subscription_status' => $company->subscriptionActive() ? 'active' : 'expired',
                'trial_status' => $company->isOnTrial() ? 'active' : ($company->trialExpired() ? 'expired' : 'not_started'),
                'days_until_expiry' => $company->subscription_ends_at ? $company->subscription_ends_at->diffInDays(now()) : null,
            ];

            return response()->json([
                'success' => true,
                'data' => [
                    'company' => $company,
                    'stats' => $stats
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في جلب بيانات الشركة'
            ], 500);
        }
    }

    /**
     * تحديث بيانات الشركة
     */
    public function update(Request $request, $id): JsonResponse
    {
        try {
            $company = Company::find($id);

            if (!$company) {
                return response()->json([
                    'success' => false,
                    'message' => 'الشركة غير موجودة'
                ], 404);
            }

            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'name_en' => 'nullable|string|max:255',
                'commercial_register' => 'required|string|unique:companies,commercial_register,' . $id,
                'tax_number' => 'required|string|unique:companies,tax_number,' . $id,
                'email' => 'required|email|unique:companies,email,' . $id,
                'phone' => 'required|string|max:20',
                'address' => 'required|string',
                'address_en' => 'nullable|string',
                'city' => 'required|string|max:100',
                'country' => 'nullable|string|max:100',
                'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
                'subscription_price' => 'nullable|numeric|min:0',
                'max_branches' => 'nullable|integer|min:1',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صحيحة',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $request->except(['logo']);

            // رفع الشعار الجديد
            if ($request->hasFile('logo')) {
                // حذف الشعار القديم
                if ($company->logo) {
                    Storage::disk('public')->delete($company->logo);
                }

                $logo = $request->file('logo');
                $logoName = time() . '_' . Str::random(10) . '.' . $logo->getClientOriginalExtension();
                $logoPath = $logo->storeAs('companies/logos', $logoName, 'public');
                $data['logo'] = $logoPath;
            }

            $company->update($data);

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث بيانات الشركة بنجاح',
                'data' => $company->load(['branches', 'users', 'activeSubscription'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في تحديث بيانات الشركة'
            ], 500);
        }
    }

    /**
     * حذف شركة
     */
    public function destroy($id): JsonResponse
    {
        try {
            $company = Company::with(['users', 'branches', 'sales'])->find($id);

            if (!$company) {
                return response()->json([
                    'success' => false,
                    'message' => 'الشركة غير موجودة'
                ], 404);
            }

            // التحقق من وجود مبيعات
            if ($company->sales()->exists()) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا يمكن حذف الشركة لوجود مبيعات مرتبطة بها'
                ], 400);
            }

            DB::beginTransaction();

            try {
                // حذف الشعار
                if ($company->logo) {
                    Storage::disk('public')->delete($company->logo);
                }

                // حذف المستخدمين
                $company->users()->delete();

                // حذف الفروع
                $company->branches()->delete();

                // حذف الاشتراكات
                $company->subscriptions()->delete();

                // حذف الشركة
                $company->delete();

                DB::commit();

                return response()->json([
                    'success' => true,
                    'message' => 'تم حذف الشركة بنجاح'
                ]);

            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في حذف الشركة'
            ], 500);
        }
    }

    /**
     * تعليق الشركة
     */
    public function suspend($id): JsonResponse
    {
        try {
            $company = Company::find($id);

            if (!$company) {
                return response()->json([
                    'success' => false,
                    'message' => 'الشركة غير موجودة'
                ], 404);
            }

            $company->update(['status' => 'suspended']);

            return response()->json([
                'success' => true,
                'message' => 'تم تعليق الشركة بنجاح',
                'data' => $company
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في تعليق الشركة'
            ], 500);
        }
    }

    /**
     * تفعيل الشركة
     */
    public function activate($id): JsonResponse
    {
        try {
            $company = Company::find($id);

            if (!$company) {
                return response()->json([
                    'success' => false,
                    'message' => 'الشركة غير موجودة'
                ], 404);
            }

            $company->update(['status' => 'active']);

            return response()->json([
                'success' => true,
                'message' => 'تم تفعيل الشركة بنجاح',
                'data' => $company
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في تفعيل الشركة'
            ], 500);
        }
    }
}
