<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء الصلاحيات
        $permissions = [
            // إدارة الشركات
            'companies.view',
            'companies.create',
            'companies.edit',
            'companies.delete',
            'companies.suspend',

            // إدارة الفروع
            'branches.view',
            'branches.create',
            'branches.edit',
            'branches.delete',

            // إدارة المستخدمين
            'users.view',
            'users.create',
            'users.edit',
            'users.delete',
            'users.manage_roles',

            // إدارة المنتجات
            'products.view',
            'products.create',
            'products.edit',
            'products.delete',
            'products.manage_inventory',

            // إدارة الفئات
            'categories.view',
            'categories.create',
            'categories.edit',
            'categories.delete',

            // إدارة العملاء
            'customers.view',
            'customers.create',
            'customers.edit',
            'customers.delete',

            // إدارة الموردين
            'suppliers.view',
            'suppliers.create',
            'suppliers.edit',
            'suppliers.delete',

            // إدارة المبيعات
            'sales.view',
            'sales.create',
            'sales.edit',
            'sales.delete',
            'sales.refund',

            // نقاط البيع
            'pos.access',
            'pos.create_sale',
            'pos.process_payment',

            // التقارير
            'reports.view',
            'reports.sales',
            'reports.inventory',
            'reports.customers',
            'reports.financial',

            // الإعدادات
            'settings.view',
            'settings.edit',
            'settings.system',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // إنشاء الأدوار
        $roles = [
            'super_admin' => [
                'name' => 'مدير عام',
                'permissions' => Permission::all()->pluck('name')->toArray()
            ],
            'admin' => [
                'name' => 'مدير',
                'permissions' => [
                    'branches.view', 'branches.create', 'branches.edit',
                    'users.view', 'users.create', 'users.edit', 'users.manage_roles',
                    'products.view', 'products.create', 'products.edit', 'products.manage_inventory',
                    'categories.view', 'categories.create', 'categories.edit',
                    'customers.view', 'customers.create', 'customers.edit',
                    'suppliers.view', 'suppliers.create', 'suppliers.edit',
                    'sales.view', 'sales.create', 'sales.edit', 'sales.refund',
                    'pos.access', 'pos.create_sale', 'pos.process_payment',
                    'reports.view', 'reports.sales', 'reports.inventory', 'reports.customers', 'reports.financial',
                    'settings.view', 'settings.edit',
                ]
            ],
            'supervisor' => [
                'name' => 'مشرف',
                'permissions' => [
                    'branches.view',
                    'users.view',
                    'products.view', 'products.edit', 'products.manage_inventory',
                    'categories.view',
                    'customers.view', 'customers.create', 'customers.edit',
                    'suppliers.view',
                    'sales.view', 'sales.create', 'sales.edit',
                    'pos.access', 'pos.create_sale', 'pos.process_payment',
                    'reports.view', 'reports.sales', 'reports.inventory',
                ]
            ],
            'manager' => [
                'name' => 'مدير فرع',
                'permissions' => [
                    'users.view',
                    'products.view', 'products.manage_inventory',
                    'categories.view',
                    'customers.view', 'customers.create', 'customers.edit',
                    'sales.view', 'sales.create', 'sales.edit',
                    'pos.access', 'pos.create_sale', 'pos.process_payment',
                    'reports.view', 'reports.sales',
                ]
            ],
            'cashier' => [
                'name' => 'كاشير',
                'permissions' => [
                    'products.view',
                    'customers.view', 'customers.create',
                    'sales.view', 'sales.create',
                    'pos.access', 'pos.create_sale', 'pos.process_payment',
                ]
            ],
            'accountant' => [
                'name' => 'محاسب',
                'permissions' => [
                    'sales.view',
                    'customers.view',
                    'suppliers.view',
                    'reports.view', 'reports.sales', 'reports.financial',
                ]
            ],
        ];

        foreach ($roles as $roleKey => $roleData) {
            $role = Role::firstOrCreate(
                ['name' => $roleKey],
                ['display_name' => $roleData['name']]
            );

            $role->syncPermissions($roleData['permissions']);
        }

        // إنشاء مستخدم مدير عام
        $superAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'مدير النظام',
                'password' => Hash::make('password'),
                'user_type' => 'super_admin',
                'status' => 'active',
            ]
        );

        $superAdmin->assignRole('super_admin');

        $this->command->info('تم إنشاء الأدوار والصلاحيات بنجاح');
        $this->command->info('بيانات المدير العام:');
        $this->command->info('البريد الإلكتروني: <EMAIL>');
        $this->command->info('كلمة المرور: password');
    }
}
