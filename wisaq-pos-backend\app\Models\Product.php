<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;

class Product extends Model
{
    use SoftDeletes;

    /**
     * الحقول القابلة للتعبئة
     */
    protected $fillable = [
        'company_id',
        'category_id',
        'name',
        'name_en',
        'description',
        'description_en',
        'sku',
        'barcode',
        'image',
        'images',
        'cost_price',
        'selling_price',
        'discount_price',
        'tax_type',
        'tax_rate',
        'unit',
        'track_quantity',
        'quantity',
        'min_quantity',
        'max_quantity',
        'expiry_date',
        'is_active',
        'is_featured',
        'attributes',
    ];

    /**
     * تحويل الحقول
     */
    protected $casts = [
        'images' => 'array',
        'cost_price' => 'decimal:2',
        'selling_price' => 'decimal:2',
        'discount_price' => 'decimal:2',
        'tax_rate' => 'decimal:2',
        'track_quantity' => 'boolean',
        'quantity' => 'integer',
        'min_quantity' => 'integer',
        'max_quantity' => 'integer',
        'expiry_date' => 'date',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'attributes' => 'array',
    ];

    /**
     * العلاقات
     */

    /**
     * الشركة التابع لها المنتج
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * الفئة التابع لها المنتج
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * بنود المبيعات للمنتج
     */
    public function saleItems(): HasMany
    {
        return $this->hasMany(SaleItem::class);
    }

    /**
     * سجلات المخزون للمنتج
     */
    public function inventoryLogs(): HasMany
    {
        return $this->hasMany(InventoryLog::class);
    }

    /**
     * Scopes
     */

    /**
     * المنتجات النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * المنتجات المميزة
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * المنتجات في شركة معينة
     */
    public function scopeInCompany($query, $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    /**
     * المنتجات في فئة معينة
     */
    public function scopeInCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * المنتجات منخفضة المخزون
     */
    public function scopeLowStock($query)
    {
        return $query->where('track_quantity', true)
                    ->whereColumn('quantity', '<=', 'min_quantity');
    }

    /**
     * المنتجات منتهية الصلاحية
     */
    public function scopeExpired($query)
    {
        return $query->whereNotNull('expiry_date')
                    ->where('expiry_date', '<', now());
    }

    /**
     * المنتجات قريبة الانتهاء
     */
    public function scopeExpiringSoon($query, $days = 30)
    {
        return $query->whereNotNull('expiry_date')
                    ->where('expiry_date', '>', now())
                    ->where('expiry_date', '<=', now()->addDays($days));
    }

    /**
     * البحث في المنتجات
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('name_en', 'like', "%{$search}%")
              ->orWhere('sku', 'like', "%{$search}%")
              ->orWhere('barcode', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%");
        });
    }

    /**
     * Helper Methods
     */

    /**
     * هل المنتج نشط؟
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    /**
     * هل المنتج مميز؟
     */
    public function isFeatured(): bool
    {
        return $this->is_featured;
    }

    /**
     * هل المنتج منخفض المخزون؟
     */
    public function isLowStock(): bool
    {
        return $this->track_quantity && $this->quantity <= $this->min_quantity;
    }

    /**
     * هل المنتج نفد من المخزون؟
     */
    public function isOutOfStock(): bool
    {
        return $this->track_quantity && $this->quantity <= 0;
    }

    /**
     * هل المنتج منتهي الصلاحية؟
     */
    public function isExpired(): bool
    {
        return $this->expiry_date && $this->expiry_date->isPast();
    }

    /**
     * هل المنتج قريب الانتهاء؟
     */
    public function isExpiringSoon($days = 30): bool
    {
        return $this->expiry_date &&
               $this->expiry_date->isFuture() &&
               $this->expiry_date->diffInDays(now()) <= $days;
    }

    /**
     * الحصول على السعر النهائي (مع الخصم إن وجد)
     */
    public function getFinalPriceAttribute(): float
    {
        return $this->discount_price ?? $this->selling_price;
    }

    /**
     * الحصول على قيمة الضريبة
     */
    public function getTaxAmountAttribute(): float
    {
        $price = $this->final_price;

        if ($this->tax_type === 'inclusive') {
            return $price - ($price / (1 + ($this->tax_rate / 100)));
        } elseif ($this->tax_type === 'exclusive') {
            return $price * ($this->tax_rate / 100);
        }

        return 0;
    }

    /**
     * الحصول على السعر بدون ضريبة
     */
    public function getPriceWithoutTaxAttribute(): float
    {
        $price = $this->final_price;

        if ($this->tax_type === 'inclusive') {
            return $price / (1 + ($this->tax_rate / 100));
        }

        return $price;
    }

    /**
     * الحصول على السعر مع الضريبة
     */
    public function getPriceWithTaxAttribute(): float
    {
        $price = $this->final_price;

        if ($this->tax_type === 'exclusive') {
            return $price * (1 + ($this->tax_rate / 100));
        }

        return $price;
    }

    /**
     * تحديث كمية المخزون
     */
    public function updateQuantity(int $change, string $type = 'adjustment', $reference = null, $userId = null, $branchId = null): void
    {
        if (!$this->track_quantity) {
            return;
        }

        $oldQuantity = $this->quantity;
        $newQuantity = $oldQuantity + $change;

        $this->update(['quantity' => max(0, $newQuantity)]);

        // تسجيل في سجل المخزون
        $this->inventoryLogs()->create([
            'company_id' => $this->company_id,
            'branch_id' => $branchId ?? (Auth::check() ? Auth::user()->branch_id : null),
            'user_id' => $userId ?? (Auth::check() ? Auth::user()->id : null),
            'type' => $type,
            'quantity_before' => $oldQuantity,
            'quantity_change' => $change,
            'quantity_after' => $this->quantity,
            'reference_type' => $reference ? get_class($reference) : null,
            'reference_id' => $reference?->id,
        ]);
    }

    /**
     * الحصول على حالة المخزون
     */
    public function getStockStatusAttribute(): string
    {
        if (!$this->track_quantity) {
            return 'unlimited';
        }

        if ($this->quantity <= 0) {
            return 'out_of_stock';
        } elseif ($this->quantity <= $this->min_quantity) {
            return 'low_stock';
        } elseif ($this->max_quantity && $this->quantity >= $this->max_quantity) {
            return 'overstock';
        }

        return 'in_stock';
    }
}
