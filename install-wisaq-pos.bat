@echo off
title Wisaq POS System Installer
color 0B

echo.
echo ========================================
echo    WISAQ POS SYSTEM INSTALLER
echo ========================================
echo.

echo [1/8] Checking system requirements...
echo.

echo Checking PHP...
php --version
if %errorlevel% neq 0 (
    echo ERROR: PHP is not installed or not in PATH
    echo Please install PHP 8.1 or higher
    pause
    exit /b 1
)

echo.
echo Checking Composer...
composer --version
if %errorlevel% neq 0 (
    echo ERROR: Composer is not installed or not in PATH
    echo Please install Composer
    pause
    exit /b 1
)

echo.
echo Checking Node.js...
node --version
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js 18 or higher
    pause
    exit /b 1
)

echo.
echo Checking NPM...
npm --version
if %errorlevel% neq 0 (
    echo ERROR: NPM is not installed or not in PATH
    pause
    exit /b 1
)

echo.
echo [2/8] Installing Backend dependencies...
cd wisaq-pos-backend
composer install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install backend dependencies
    pause
    exit /b 1
)

echo.
echo [3/8] Setting up Backend environment...
if not exist .env (
    copy .env.example .env
    echo .env file created from .env.example
)

echo.
echo [4/8] Generating application key...
php artisan key:generate
if %errorlevel% neq 0 (
    echo ERROR: Failed to generate application key
    pause
    exit /b 1
)

echo.
echo [5/8] Installing Frontend dependencies...
cd ../wisaq-pos-frontend
npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install frontend dependencies
    pause
    exit /b 1
)

echo.
echo [6/8] Setting up Frontend environment...
if not exist .env (
    copy .env.example .env
    echo .env file created from .env.example
)

echo.
echo [7/8] Building Frontend...
npm run build
if %errorlevel% neq 0 (
    echo WARNING: Failed to build frontend (this is normal for development)
)

cd ..

echo.
echo [8/8] Installation completed!
echo.
echo ========================================
echo    INSTALLATION COMPLETED
echo ========================================
echo.
echo Next steps:
echo 1. Configure your database in wisaq-pos-backend/.env
echo 2. Run: php artisan migrate --seed
echo 3. Start the system using start-wisaq-pos.bat
echo.
echo Default login credentials:
echo Email: <EMAIL>
echo Password: password
echo.
echo Press any key to continue...
pause > nul
