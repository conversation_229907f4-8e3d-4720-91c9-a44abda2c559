@echo off
title Wisaq POS System Launcher
color 0A

echo.
echo ========================================
echo    WISAQ POS SYSTEM LAUNCHER
echo ========================================
echo.
echo Starting Wisaq POS System...
echo.

echo [1/3] Starting Backend (Laravel)...
start "Wisaq Backend" cmd /k "cd wisaq-pos-backend && php artisan serve --host=0.0.0.0 --port=8000"

echo [2/3] Waiting for backend to start...
timeout /t 5 /nobreak > nul

echo [3/3] Starting Frontend (React)...
start "Wisaq Frontend" cmd /k "cd wisaq-pos-frontend && npm run dev"

echo.
echo ========================================
echo    WISAQ POS SYSTEM STARTED
echo ========================================
echo.
echo Backend URL: http://localhost:8000
echo Frontend URL: http://localhost:5173
echo.
echo Default Login Credentials:
echo Email: <EMAIL>
echo Password: password
echo.
echo Press any key to close this window...
pause > nul
