@echo off
echo Starting Wisaq POS Backend...
echo.

cd wisaq-pos-backend

echo Checking PHP version...
php --version
echo.

echo Installing/updating dependencies...
composer install --no-dev --optimize-autoloader
echo.

echo Running database migrations...
php artisan migrate --force
echo.

echo Seeding database...
php artisan db:seed --force
echo.

echo Clearing cache...
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear
echo.

echo Starting Laravel server...
php artisan serve --host=0.0.0.0 --port=8000

pause
