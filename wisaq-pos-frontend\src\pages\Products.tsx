import React, { useState } from 'react';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  PhotoIcon,
} from '@heroicons/react/24/outline';
import { useTheme } from '../contexts/ThemeContext';
import { useProducts } from '../hooks/useApi';
import { Product } from '../types';

const Products: React.FC = () => {
  const { isRTL } = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);

  const { data: products, loading, error, updateParams } = useProducts({
    search: searchQuery,
    category_id: selectedCategory,
    status: statusFilter,
  });

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    updateParams({ search: query });
  };

  const handleCategoryFilter = (categoryId: string) => {
    setSelectedCategory(categoryId);
    updateParams({ category_id: categoryId });
  };

  const handleStatusFilter = (status: string) => {
    setStatusFilter(status);
    updateParams({ status });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(isRTL ? 'ar-SA' : 'en-US', {
      style: 'currency',
      currency: 'SAR',
    }).format(amount);
  };

  const getStockStatusColor = (status: string) => {
    switch (status) {
      case 'in_stock':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'low_stock':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'out_of_stock':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getStockStatusText = (status: string) => {
    switch (status) {
      case 'in_stock':
        return isRTL ? 'متوفر' : 'In Stock';
      case 'low_stock':
        return isRTL ? 'مخزون منخفض' : 'Low Stock';
      case 'out_of_stock':
        return isRTL ? 'نفد المخزون' : 'Out of Stock';
      case 'unlimited':
        return isRTL ? 'غير محدود' : 'Unlimited';
      default:
        return status;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-48 animate-pulse"></div>
          <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-32 animate-pulse"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {[...Array(8)].map((_, i) => (
            <div key={i} className="card p-6 animate-pulse">
              <div className="h-48 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            {isRTL ? 'إدارة المنتجات' : 'Products Management'}
          </h1>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {isRTL ? 'إدارة وتنظيم منتجات متجرك' : 'Manage and organize your store products'}
          </p>
        </div>
        <button
          onClick={() => setShowAddModal(true)}
          className="btn-primary flex items-center gap-2"
        >
          <PlusIcon className="h-5 w-5" />
          {isRTL ? 'إضافة منتج' : 'Add Product'}
        </button>
      </div>

      {/* Filters and Search */}
      <div className="card p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Search */}
          <div className="relative">
            <MagnifyingGlassIcon className={`absolute top-3 ${isRTL ? 'right-3' : 'left-3'} h-5 w-5 text-gray-400`} />
            <input
              type="text"
              placeholder={isRTL ? 'البحث في المنتجات...' : 'Search products...'}
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className={`input-field ${isRTL ? 'pr-10' : 'pl-10'}`}
            />
          </div>

          {/* Category Filter */}
          <select
            value={selectedCategory}
            onChange={(e) => handleCategoryFilter(e.target.value)}
            className="input-field"
          >
            <option value="">{isRTL ? 'جميع الفئات' : 'All Categories'}</option>
            <option value="1">{isRTL ? 'إلكترونيات' : 'Electronics'}</option>
            <option value="2">{isRTL ? 'ملابس' : 'Clothing'}</option>
            <option value="3">{isRTL ? 'طعام ومشروبات' : 'Food & Beverages'}</option>
          </select>

          {/* Status Filter */}
          <select
            value={statusFilter}
            onChange={(e) => handleStatusFilter(e.target.value)}
            className="input-field"
          >
            <option value="">{isRTL ? 'جميع الحالات' : 'All Status'}</option>
            <option value="active">{isRTL ? 'نشط' : 'Active'}</option>
            <option value="inactive">{isRTL ? 'غير نشط' : 'Inactive'}</option>
            <option value="low_stock">{isRTL ? 'مخزون منخفض' : 'Low Stock'}</option>
            <option value="out_of_stock">{isRTL ? 'نفد المخزون' : 'Out of Stock'}</option>
          </select>

          {/* Advanced Filters */}
          <button className="btn-secondary flex items-center gap-2">
            <FunnelIcon className="h-5 w-5" />
            {isRTL ? 'فلاتر متقدمة' : 'Advanced Filters'}
          </button>
        </div>
      </div>

      {/* Products Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {products?.map((product: Product) => (
          <div key={product.id} className="card overflow-hidden hover:shadow-lg transition-shadow duration-200">
            {/* Product Image */}
            <div className="relative h-48 bg-gray-100 dark:bg-gray-700">
              {product.image ? (
                <img
                  src={product.image}
                  alt={product.name}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <PhotoIcon className="h-16 w-16 text-gray-400" />
                </div>
              )}
              
              {/* Featured Badge */}
              {product.is_featured && (
                <div className="absolute top-2 left-2">
                  <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                    {isRTL ? 'مميز' : 'Featured'}
                  </span>
                </div>
              )}

              {/* Stock Status */}
              <div className="absolute top-2 right-2">
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStockStatusColor(product.stock_status || 'in_stock')}`}>
                  {getStockStatusText(product.stock_status || 'in_stock')}
                </span>
              </div>
            </div>

            {/* Product Info */}
            <div className="p-4">
              <div className="flex items-start justify-between mb-2">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white truncate">
                  {product.name}
                </h3>
                <div className="flex items-center gap-1">
                  <button className="p-1 text-gray-400 hover:text-blue-500 transition-colors">
                    <EyeIcon className="h-4 w-4" />
                  </button>
                  <button className="p-1 text-gray-400 hover:text-green-500 transition-colors">
                    <PencilIcon className="h-4 w-4" />
                  </button>
                  <button className="p-1 text-gray-400 hover:text-red-500 transition-colors">
                    <TrashIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>

              <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                {isRTL ? 'رمز المنتج:' : 'SKU:'} {product.sku}
              </p>

              <div className="flex items-center justify-between mb-2">
                <span className="text-lg font-bold text-primary-600 dark:text-primary-400">
                  {formatCurrency(product.selling_price)}
                </span>
                {product.discount_price && (
                  <span className="text-sm text-gray-500 line-through">
                    {formatCurrency(product.discount_price)}
                  </span>
                )}
              </div>

              {product.track_quantity && (
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-500 dark:text-gray-400">
                    {isRTL ? 'الكمية:' : 'Quantity:'}
                  </span>
                  <span className={`font-medium ${
                    product.quantity <= product.min_quantity 
                      ? 'text-red-600 dark:text-red-400' 
                      : 'text-gray-900 dark:text-white'
                  }`}>
                    {product.quantity} {product.unit}
                  </span>
                </div>
              )}

              {product.category && (
                <div className="mt-2">
                  <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                    {product.category.name}
                  </span>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {!loading && (!products || products.length === 0) && (
        <div className="text-center py-12">
          <PhotoIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
            {isRTL ? 'لا توجد منتجات' : 'No products found'}
          </h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {isRTL ? 'ابدأ بإضافة منتج جديد' : 'Get started by adding a new product'}
          </p>
          <div className="mt-6">
            <button
              onClick={() => setShowAddModal(true)}
              className="btn-primary flex items-center gap-2 mx-auto"
            >
              <PlusIcon className="h-5 w-5" />
              {isRTL ? 'إضافة منتج' : 'Add Product'}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default Products;
