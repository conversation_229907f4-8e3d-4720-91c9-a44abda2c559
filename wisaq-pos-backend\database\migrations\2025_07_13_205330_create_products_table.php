<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')->constrained()->onDelete('cascade');
            $table->foreignId('category_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->string('name_en')->nullable();
            $table->text('description')->nullable();
            $table->text('description_en')->nullable();
            $table->string('sku')->unique(); // رمز المنتج
            $table->string('barcode')->unique()->nullable(); // الباركود
            $table->string('image')->nullable();
            $table->json('images')->nullable(); // صور إضافية
            $table->decimal('cost_price', 10, 2)->default(0); // سعر التكلفة
            $table->decimal('selling_price', 10, 2); // سعر البيع
            $table->decimal('discount_price', 10, 2)->nullable(); // سعر مخفض
            $table->enum('tax_type', ['inclusive', 'exclusive', 'none'])->default('inclusive');
            $table->decimal('tax_rate', 5, 2)->default(15.00); // معدل الضريبة
            $table->string('unit', 50)->default('piece'); // الوحدة (قطعة، كيلو، لتر، إلخ)
            $table->boolean('track_quantity')->default(true); // تتبع الكمية
            $table->integer('quantity')->default(0); // الكمية الحالية
            $table->integer('min_quantity')->default(5); // الحد الأدنى للكمية
            $table->integer('max_quantity')->nullable(); // الحد الأقصى للكمية
            $table->date('expiry_date')->nullable(); // تاريخ انتهاء الصلاحية
            $table->boolean('is_active')->default(true);
            $table->boolean('is_featured')->default(false); // منتج مميز
            $table->json('attributes')->nullable(); // خصائص إضافية (لون، حجم، إلخ)
            $table->timestamps();

            // Indexes
            $table->index(['company_id', 'category_id', 'is_active']);
            $table->index(['sku', 'company_id']);
            $table->index(['barcode', 'company_id']);
            $table->index(['quantity', 'min_quantity']);
            $table->index('expiry_date');
            $table->fullText(['name', 'description', 'sku']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
