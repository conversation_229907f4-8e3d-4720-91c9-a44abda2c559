<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('key'); // مفتاح الإعداد
            $table->text('value')->nullable(); // قيمة الإعداد
            $table->string('type')->default('string'); // نوع البيانات (string, integer, boolean, json)
            $table->string('group')->default('general'); // مجموعة الإعدادات
            $table->text('description')->nullable(); // وصف الإعداد
            $table->boolean('is_public')->default(false); // هل الإعداد عام أم خاص
            $table->timestamps();

            // Indexes
            $table->unique(['company_id', 'key']);
            $table->index(['group', 'is_public']);
            $table->index('key');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('settings');
    }
};
