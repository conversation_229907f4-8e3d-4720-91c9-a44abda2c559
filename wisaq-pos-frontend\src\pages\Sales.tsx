import React, { useState } from 'react';
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  EyeIcon,
  PrinterIcon,
  ArrowPathIcon,
  CalendarDaysIcon,
  DocumentTextIcon,
  CurrencyDollarIcon,
} from '@heroicons/react/24/outline';
import { useTheme } from '../contexts/ThemeContext';
import { useSales } from '../hooks/useApi';
import { Sale } from '../types';

const Sales: React.FC = () => {
  const { isRTL } = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [dateFrom, setDateFrom] = useState('');
  const [dateTo, setDateTo] = useState('');
  const [selectedSale, setSelectedSale] = useState<Sale | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  const { data: sales, loading, error, updateParams } = useSales({
    search: searchQuery,
    status: statusFilter,
    date_from: dateFrom,
    date_to: dateTo,
  });

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    updateParams({ search: query });
  };

  const handleStatusFilter = (status: string) => {
    setStatusFilter(status);
    updateParams({ status });
  };

  const handleDateFilter = () => {
    updateParams({ date_from: dateFrom, date_to: dateTo });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(isRTL ? 'ar-SA' : 'en-US', {
      style: 'currency',
      currency: 'SAR',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(isRTL ? 'ar-SA' : 'en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'refunded':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return isRTL ? 'مكتملة' : 'Completed';
      case 'pending':
        return isRTL ? 'معلقة' : 'Pending';
      case 'cancelled':
        return isRTL ? 'ملغاة' : 'Cancelled';
      case 'refunded':
        return isRTL ? 'مسترجعة' : 'Refunded';
      default:
        return status;
    }
  };

  const getPaymentMethodText = (method: string) => {
    switch (method) {
      case 'cash':
        return isRTL ? 'نقدي' : 'Cash';
      case 'card':
        return isRTL ? 'بطاقة' : 'Card';
      case 'transfer':
        return isRTL ? 'تحويل' : 'Transfer';
      case 'credit':
        return isRTL ? 'آجل' : 'Credit';
      case 'mixed':
        return isRTL ? 'مختلط' : 'Mixed';
      default:
        return method;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-48 animate-pulse"></div>
          <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-32 animate-pulse"></div>
        </div>
        <div className="card p-6">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full mb-4 animate-pulse"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            {isRTL ? 'إدارة المبيعات' : 'Sales Management'}
          </h1>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {isRTL ? 'عرض وإدارة جميع المبيعات' : 'View and manage all sales transactions'}
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="card p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="bg-green-500 p-3 rounded-lg">
                <CurrencyDollarIcon className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className={`${isRTL ? 'mr-4' : 'ml-4'} flex-1`}>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {isRTL ? 'مبيعات اليوم' : "Today's Sales"}
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {formatCurrency(15420)}
              </p>
            </div>
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="bg-blue-500 p-3 rounded-lg">
                <DocumentTextIcon className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className={`${isRTL ? 'mr-4' : 'ml-4'} flex-1`}>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {isRTL ? 'عدد الفواتير' : 'Total Invoices'}
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {sales?.length || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="bg-purple-500 p-3 rounded-lg">
                <CalendarDaysIcon className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className={`${isRTL ? 'mr-4' : 'ml-4'} flex-1`}>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {isRTL ? 'مبيعات الشهر' : 'Monthly Sales'}
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {formatCurrency(245680)}
              </p>
            </div>
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="bg-orange-500 p-3 rounded-lg">
                <ArrowPathIcon className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className={`${isRTL ? 'mr-4' : 'ml-4'} flex-1`}>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {isRTL ? 'المرتجعات' : 'Returns'}
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {formatCurrency(1250)}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="card p-6">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          {/* Search */}
          <div className="relative">
            <MagnifyingGlassIcon className={`absolute top-3 ${isRTL ? 'right-3' : 'left-3'} h-5 w-5 text-gray-400`} />
            <input
              type="text"
              placeholder={isRTL ? 'البحث برقم الفاتورة...' : 'Search by invoice number...'}
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className={`input-field ${isRTL ? 'pr-10' : 'pl-10'}`}
            />
          </div>

          {/* Status Filter */}
          <select
            value={statusFilter}
            onChange={(e) => handleStatusFilter(e.target.value)}
            className="input-field"
          >
            <option value="">{isRTL ? 'جميع الحالات' : 'All Status'}</option>
            <option value="completed">{isRTL ? 'مكتملة' : 'Completed'}</option>
            <option value="pending">{isRTL ? 'معلقة' : 'Pending'}</option>
            <option value="cancelled">{isRTL ? 'ملغاة' : 'Cancelled'}</option>
            <option value="refunded">{isRTL ? 'مسترجعة' : 'Refunded'}</option>
          </select>

          {/* Date From */}
          <input
            type="date"
            value={dateFrom}
            onChange={(e) => setDateFrom(e.target.value)}
            className="input-field"
            placeholder={isRTL ? 'من تاريخ' : 'From Date'}
          />

          {/* Date To */}
          <input
            type="date"
            value={dateTo}
            onChange={(e) => setDateTo(e.target.value)}
            className="input-field"
            placeholder={isRTL ? 'إلى تاريخ' : 'To Date'}
          />

          {/* Apply Filters */}
          <button
            onClick={handleDateFilter}
            className="btn-secondary flex items-center gap-2"
          >
            <FunnelIcon className="h-5 w-5" />
            {isRTL ? 'تطبيق' : 'Apply'}
          </button>
        </div>
      </div>

      {/* Sales Table */}
      <div className="card overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {isRTL ? 'رقم الفاتورة' : 'Invoice Number'}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {isRTL ? 'العميل' : 'Customer'}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {isRTL ? 'المبلغ' : 'Amount'}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {isRTL ? 'طريقة الدفع' : 'Payment Method'}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {isRTL ? 'الحالة' : 'Status'}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {isRTL ? 'التاريخ' : 'Date'}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {isRTL ? 'الإجراءات' : 'Actions'}
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
              {sales?.map((sale: Sale) => (
                <tr key={sale.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {sale.invoice_number}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-white">
                      {sale.customer?.name || (isRTL ? 'عميل نقدي' : 'Walk-in Customer')}
                    </div>
                    {sale.customer?.phone && (
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {sale.customer.phone}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {formatCurrency(sale.total_amount)}
                    </div>
                    {sale.discount_amount > 0 && (
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {isRTL ? 'خصم:' : 'Discount:'} {formatCurrency(sale.discount_amount)}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm text-gray-900 dark:text-white">
                      {getPaymentMethodText(sale.payment_method)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(sale.status)}`}>
                      {getStatusText(sale.status)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {formatDate(sale.sale_date)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => {
                          setSelectedSale(sale);
                          setShowDetailsModal(true);
                        }}
                        className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                        title={isRTL ? 'عرض التفاصيل' : 'View Details'}
                      >
                        <EyeIcon className="h-4 w-4" />
                      </button>
                      <button
                        className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                        title={isRTL ? 'طباعة' : 'Print'}
                      >
                        <PrinterIcon className="h-4 w-4" />
                      </button>
                      {sale.status === 'completed' && (
                        <button
                          className="text-orange-600 hover:text-orange-900 dark:text-orange-400 dark:hover:text-orange-300"
                          title={isRTL ? 'استرداد' : 'Refund'}
                        >
                          <ArrowPathIcon className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Empty State */}
        {!loading && (!sales || sales.length === 0) && (
          <div className="text-center py-12">
            <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
              {isRTL ? 'لا توجد مبيعات' : 'No sales found'}
            </h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {isRTL ? 'لم يتم العثور على مبيعات بالمعايير المحددة' : 'No sales found matching the specified criteria'}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Sales;
