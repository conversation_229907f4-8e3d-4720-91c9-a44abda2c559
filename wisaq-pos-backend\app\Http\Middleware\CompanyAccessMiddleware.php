<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CompanyAccessMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();

        // السماح للمدير العام بالوصول لكل شيء
        if ($user->isSuperAdmin()) {
            return $next($request);
        }

        // التحقق من وجود شركة للمستخدم
        if (!$user->company_id) {
            return response()->json([
                'success' => false,
                'message' => 'المستخدم غير مرتبط بأي شركة'
            ], 403);
        }

        // التحقق من حالة الشركة
        $company = $user->company;
        if (!$company || $company->status !== 'active') {
            return response()->json([
                'success' => false,
                'message' => 'حساب الشركة غير نشط'
            ], 403);
        }

        // التحقق من انتهاء الاشتراك
        if ($company->subscriptionExpired()) {
            return response()->json([
                'success' => false,
                'message' => 'انتهت صلاحية اشتراك الشركة'
            ], 403);
        }

        // إضافة معرف الشركة للطلب
        $request->merge(['company_id' => $user->company_id]);

        return $next($request);
    }
}
