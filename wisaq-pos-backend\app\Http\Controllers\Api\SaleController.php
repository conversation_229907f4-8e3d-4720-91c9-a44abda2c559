<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Sale;
use App\Models\SaleItem;
use App\Models\Product;
use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class SaleController extends Controller
{
    /**
     * عرض قائمة المبيعات
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;
            $branchId = Auth::user()->branch_id;
            $perPage = $request->get('per_page', 15);

            $query = Sale::with(['user', 'customer', 'branch', 'items.product'])
                ->inCompany($companyId);

            // فلترة حسب الفرع (إذا لم يكن مدير عام)
            if (!Auth::user()->isSuperAdmin() && $branchId) {
                $query->inBranch($branchId);
            } elseif ($request->has('branch_id') && $request->branch_id) {
                $query->inBranch($request->branch_id);
            }

            // فلترة حسب الحالة
            if ($request->has('status') && $request->status) {
                $query->where('status', $request->status);
            }

            // فلترة حسب نوع المبيعة
            if ($request->has('type') && $request->type) {
                $query->where('type', $request->type);
            }

            // فلترة حسب التاريخ
            if ($request->has('date_from') && $request->date_from) {
                $query->whereDate('sale_date', '>=', $request->date_from);
            }

            if ($request->has('date_to') && $request->date_to) {
                $query->whereDate('sale_date', '<=', $request->date_to);
            }

            // فلترة حسب العميل
            if ($request->has('customer_id') && $request->customer_id) {
                $query->where('customer_id', $request->customer_id);
            }

            // فلترة حسب الكاشير
            if ($request->has('user_id') && $request->user_id) {
                $query->where('user_id', $request->user_id);
            }

            // البحث في رقم الفاتورة
            if ($request->has('search') && $request->search) {
                $query->where('invoice_number', 'like', '%' . $request->search . '%');
            }

            // الترتيب
            $sortBy = $request->get('sort_by', 'sale_date');
            $sortOrder = $request->get('sort_order', 'desc');
            $query->orderBy($sortBy, $sortOrder);

            $sales = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => [
                    'sales' => $sales->items(),
                    'pagination' => [
                        'current_page' => $sales->currentPage(),
                        'last_page' => $sales->lastPage(),
                        'per_page' => $sales->perPage(),
                        'total' => $sales->total(),
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في جلب المبيعات'
            ], 500);
        }
    }

    /**
     * عرض مبيعة محددة
     */
    public function show($id): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;
            $branchId = Auth::user()->branch_id;

            $query = Sale::with(['user', 'customer', 'branch', 'items.product.category'])
                ->inCompany($companyId);

            // فلترة حسب الفرع إذا لم يكن مدير عام
            if (!Auth::user()->isSuperAdmin() && $branchId) {
                $query->inBranch($branchId);
            }

            $sale = $query->find($id);

            if (!$sale) {
                return response()->json([
                    'success' => false,
                    'message' => 'المبيعة غير موجودة'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $sale
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في جلب بيانات المبيعة'
            ], 500);
        }
    }

    /**
     * إنشاء مبيعة جديدة (POS)
     */
    public function createSale(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'customer_id' => 'nullable|exists:customers,id',
                'items' => 'required|array|min:1',
                'items.*.product_id' => 'required|exists:products,id',
                'items.*.quantity' => 'required|integer|min:1',
                'items.*.unit_price' => 'nullable|numeric|min:0',
                'discount_amount' => 'nullable|numeric|min:0',
                'discount_percentage' => 'nullable|numeric|min:0|max:100',
                'payment_method' => 'required|in:cash,card,transfer,credit,mixed',
                'payment_details' => 'nullable|array',
                'notes' => 'nullable|string|max:500',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صحيحة',
                    'errors' => $validator->errors()
                ], 422);
            }

            $companyId = Auth::user()->company_id;
            $branchId = Auth::user()->branch_id;
            $userId = Auth::id();

            if (!$branchId) {
                return response()->json([
                    'success' => false,
                    'message' => 'المستخدم غير مرتبط بأي فرع'
                ], 400);
            }

            DB::beginTransaction();

            try {
                // التحقق من العميل إذا تم تحديده
                if ($request->customer_id) {
                    $customer = Customer::where('id', $request->customer_id)
                        ->where('company_id', $companyId)
                        ->where('is_active', true)
                        ->first();

                    if (!$customer) {
                        throw new \Exception('العميل المحدد غير موجود أو غير نشط');
                    }
                }

                // إنشاء رقم الفاتورة
                $invoiceNumber = Sale::generateInvoiceNumber($companyId, $branchId);

                // إنشاء المبيعة
                $sale = Sale::create([
                    'company_id' => $companyId,
                    'branch_id' => $branchId,
                    'user_id' => $userId,
                    'customer_id' => $request->customer_id,
                    'invoice_number' => $invoiceNumber,
                    'type' => 'sale',
                    'status' => 'pending',
                    'subtotal' => 0,
                    'discount_amount' => $request->discount_amount ?? 0,
                    'discount_percentage' => $request->discount_percentage ?? 0,
                    'tax_amount' => 0,
                    'total_amount' => 0,
                    'paid_amount' => 0,
                    'change_amount' => 0,
                    'payment_method' => $request->payment_method,
                    'payment_details' => $request->payment_details,
                    'notes' => $request->notes,
                    'sale_date' => now(),
                ]);

                // إضافة البنود
                foreach ($request->items as $itemData) {
                    $product = Product::where('id', $itemData['product_id'])
                        ->where('company_id', $companyId)
                        ->where('is_active', true)
                        ->first();

                    if (!$product) {
                        throw new \Exception("المنتج غير موجود أو غير نشط");
                    }

                    // التحقق من توفر الكمية
                    if ($product->track_quantity && $product->quantity < $itemData['quantity']) {
                        throw new \Exception("الكمية المطلوبة من المنتج '{$product->name}' غير متوفرة");
                    }

                    $sale->addItem($product, $itemData['quantity'], $itemData['unit_price'] ?? null);
                }

                DB::commit();

                return response()->json([
                    'success' => true,
                    'message' => 'تم إنشاء المبيعة بنجاح',
                    'data' => $sale->load(['items.product', 'customer', 'user', 'branch'])
                ], 201);

            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * معالجة الدفع وإتمام المبيعة
     */
    public function processPayment(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'sale_id' => 'required|exists:sales,id',
                'paid_amount' => 'required|numeric|min:0',
                'payment_method' => 'required|in:cash,card,transfer,credit,mixed',
                'payment_details' => 'nullable|array',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صحيحة',
                    'errors' => $validator->errors()
                ], 422);
            }

            $companyId = Auth::user()->company_id;
            $branchId = Auth::user()->branch_id;

            $sale = Sale::where('id', $request->sale_id)
                ->where('company_id', $companyId)
                ->where('branch_id', $branchId)
                ->where('status', 'pending')
                ->first();

            if (!$sale) {
                return response()->json([
                    'success' => false,
                    'message' => 'المبيعة غير موجودة أو تم إتمامها مسبقاً'
                ], 404);
            }

            DB::beginTransaction();

            try {
                $changeAmount = max(0, $request->paid_amount - $sale->total_amount);

                $sale->update([
                    'paid_amount' => $request->paid_amount,
                    'change_amount' => $changeAmount,
                    'payment_method' => $request->payment_method,
                    'payment_details' => $request->payment_details,
                    'status' => 'completed',
                ]);

                // تحديث آخر عملية شراء للعميل
                if ($sale->customer) {
                    $sale->customer->update(['last_purchase_at' => now()]);
                }

                DB::commit();

                return response()->json([
                    'success' => true,
                    'message' => 'تم إتمام المبيعة بنجاح',
                    'data' => $sale->load(['items.product', 'customer', 'user', 'branch'])
                ]);

            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في معالجة الدفع'
            ], 500);
        }
    }

    /**
     * طباعة الفاتورة
     */
    public function print($id): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;
            $branchId = Auth::user()->branch_id;

            $query = Sale::with(['user', 'customer', 'branch', 'items.product', 'company'])
                ->inCompany($companyId);

            if (!Auth::user()->isSuperAdmin() && $branchId) {
                $query->inBranch($branchId);
            }

            $sale = $query->find($id);

            if (!$sale) {
                return response()->json([
                    'success' => false,
                    'message' => 'المبيعة غير موجودة'
                ], 404);
            }

            if ($sale->status !== 'completed') {
                return response()->json([
                    'success' => false,
                    'message' => 'لا يمكن طباعة فاتورة غير مكتملة'
                ], 400);
            }

            // تحديد حالة الطباعة
            $sale->markAsPrinted();

            return response()->json([
                'success' => true,
                'message' => 'تم تحضير الفاتورة للطباعة',
                'data' => [
                    'sale' => $sale,
                    'print_data' => [
                        'company_name' => $sale->company->name,
                        'company_address' => $sale->company->address,
                        'company_phone' => $sale->company->phone,
                        'company_tax_number' => $sale->company->tax_number,
                        'branch_name' => $sale->branch->name,
                        'branch_address' => $sale->branch->address,
                        'invoice_number' => $sale->invoice_number,
                        'sale_date' => $sale->sale_date->format('Y-m-d H:i:s'),
                        'cashier_name' => $sale->user->name,
                        'customer_name' => $sale->customer?->name ?? 'عميل نقدي',
                        'items' => $sale->items->map(function($item) {
                            return [
                                'name' => $item->product_name,
                                'quantity' => $item->quantity,
                                'unit_price' => $item->unit_price,
                                'total' => $item->total_amount,
                            ];
                        }),
                        'subtotal' => $sale->subtotal,
                        'discount_amount' => $sale->discount_amount,
                        'tax_amount' => $sale->tax_amount,
                        'total_amount' => $sale->total_amount,
                        'paid_amount' => $sale->paid_amount,
                        'change_amount' => $sale->change_amount,
                        'payment_method' => $sale->payment_method,
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في طباعة الفاتورة'
            ], 500);
        }
    }

    /**
     * استرداد مبيعة
     */
    public function refund(Request $request, $id): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'reason' => 'required|string|max:500',
                'items' => 'nullable|array',
                'items.*.sale_item_id' => 'required|exists:sale_items,id',
                'items.*.quantity' => 'required|integer|min:1',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صحيحة',
                    'errors' => $validator->errors()
                ], 422);
            }

            $companyId = Auth::user()->company_id;
            $branchId = Auth::user()->branch_id;

            $originalSale = Sale::with(['items.product'])
                ->where('id', $id)
                ->where('company_id', $companyId)
                ->where('status', 'completed')
                ->first();

            if (!$originalSale) {
                return response()->json([
                    'success' => false,
                    'message' => 'المبيعة غير موجودة أو لا يمكن استردادها'
                ], 404);
            }

            DB::beginTransaction();

            try {
                // إنشاء فاتورة استرداد
                $refundSale = Sale::create([
                    'company_id' => $companyId,
                    'branch_id' => $branchId,
                    'user_id' => Auth::id(),
                    'customer_id' => $originalSale->customer_id,
                    'invoice_number' => Sale::generateInvoiceNumber($companyId, $branchId),
                    'type' => 'return',
                    'status' => 'completed',
                    'subtotal' => 0,
                    'discount_amount' => 0,
                    'tax_amount' => 0,
                    'total_amount' => 0,
                    'paid_amount' => 0,
                    'change_amount' => 0,
                    'payment_method' => 'cash',
                    'notes' => 'استرداد للفاتورة: ' . $originalSale->invoice_number . ' - السبب: ' . $request->reason,
                    'sale_date' => now(),
                ]);

                $totalRefundAmount = 0;

                // معالجة البنود المسترجعة
                $itemsToRefund = $request->items ?? $originalSale->items->map(function($item) {
                    return [
                        'sale_item_id' => $item->id,
                        'quantity' => $item->quantity
                    ];
                })->toArray();

                foreach ($itemsToRefund as $refundItem) {
                    $originalItem = $originalSale->items->find($refundItem['sale_item_id']);

                    if (!$originalItem) {
                        throw new \Exception('بند المبيعة غير موجود');
                    }

                    if ($refundItem['quantity'] > $originalItem->quantity) {
                        throw new \Exception('الكمية المسترجعة أكبر من الكمية الأصلية');
                    }

                    // إنشاء بند الاسترداد
                    $refundSale->items()->create([
                        'product_id' => $originalItem->product_id,
                        'product_name' => $originalItem->product_name,
                        'product_sku' => $originalItem->product_sku,
                        'quantity' => -$refundItem['quantity'], // كمية سالبة للاسترداد
                        'unit_price' => $originalItem->unit_price,
                        'tax_rate' => $originalItem->tax_rate,
                        'tax_amount' => -($originalItem->tax_amount * ($refundItem['quantity'] / $originalItem->quantity)),
                        'total_amount' => -($originalItem->total_amount * ($refundItem['quantity'] / $originalItem->quantity)),
                    ]);

                    // إرجاع الكمية للمخزون
                    $originalItem->product->updateQuantity(
                        $refundItem['quantity'],
                        'return',
                        $refundSale,
                        Auth::id(),
                        $branchId
                    );

                    $totalRefundAmount += $originalItem->total_amount * ($refundItem['quantity'] / $originalItem->quantity);
                }

                // تحديث إجماليات فاتورة الاسترداد
                $refundSale->update([
                    'subtotal' => -$totalRefundAmount,
                    'total_amount' => -$totalRefundAmount,
                    'paid_amount' => -$totalRefundAmount,
                ]);

                // تحديث حالة الفاتورة الأصلية
                $originalSale->update(['status' => 'refunded']);

                DB::commit();

                return response()->json([
                    'success' => true,
                    'message' => 'تم استرداد المبيعة بنجاح',
                    'data' => [
                        'original_sale' => $originalSale,
                        'refund_sale' => $refundSale->load(['items.product']),
                        'refund_amount' => $totalRefundAmount
                    ]
                ]);

            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * البحث بالفاتورة
     */
    public function findByInvoice($invoiceNumber): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;
            $branchId = Auth::user()->branch_id;

            $query = Sale::with(['user', 'customer', 'branch', 'items.product'])
                ->inCompany($companyId)
                ->where('invoice_number', $invoiceNumber);

            if (!Auth::user()->isSuperAdmin() && $branchId) {
                $query->inBranch($branchId);
            }

            $sale = $query->first();

            if (!$sale) {
                return response()->json([
                    'success' => false,
                    'message' => 'الفاتورة غير موجودة'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $sale
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في البحث عن الفاتورة'
            ], 500);
        }
    }

    /**
     * إحصائيات لوحة التحكم
     */
    public function dashboardStats(Request $request): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;
            $branchId = Auth::user()->branch_id;
            $period = $request->get('period', 'today'); // today, week, month, year

            $dateFilter = match($period) {
                'today' => [now()->startOfDay(), now()->endOfDay()],
                'week' => [now()->startOfWeek(), now()->endOfWeek()],
                'month' => [now()->startOfMonth(), now()->endOfMonth()],
                'year' => [now()->startOfYear(), now()->endOfYear()],
                default => [now()->startOfDay(), now()->endOfDay()]
            };

            $query = Sale::inCompany($companyId)
                ->completed()
                ->whereBetween('sale_date', $dateFilter);

            if (!Auth::user()->isSuperAdmin() && $branchId) {
                $query->inBranch($branchId);
            }

            $sales = $query->get();

            $stats = [
                'period' => $period,
                'total_sales' => $sales->count(),
                'total_revenue' => $sales->sum('total_amount'),
                'total_profit' => $sales->sum(function($sale) {
                    return $sale->items->sum(function($item) {
                        $product = $item->product;
                        if ($product) {
                            $profit = ($item->unit_price - $product->cost_price) * $item->quantity;
                            return max(0, $profit);
                        }
                        return 0;
                    });
                }),
                'average_sale_value' => $sales->count() > 0 ? $sales->avg('total_amount') : 0,
                'cash_sales' => $sales->where('payment_method', 'cash')->sum('total_amount'),
                'card_sales' => $sales->where('payment_method', 'card')->sum('total_amount'),
                'top_selling_products' => $this->getTopSellingProducts($companyId, $branchId, $dateFilter),
                'hourly_sales' => $this->getHourlySales($sales),
            ];

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في جلب إحصائيات لوحة التحكم'
            ], 500);
        }
    }

    /**
     * ملخص المبيعات
     */
    public function salesSummary(Request $request): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;
            $branchId = Auth::user()->branch_id;

            $dateFrom = $request->get('date_from', now()->startOfMonth());
            $dateTo = $request->get('date_to', now()->endOfMonth());

            $query = Sale::inCompany($companyId)
                ->completed()
                ->whereBetween('sale_date', [$dateFrom, $dateTo]);

            if (!Auth::user()->isSuperAdmin() && $branchId) {
                $query->inBranch($branchId);
            }

            $sales = $query->get();
            $dailySales = $sales->groupBy(function($sale) {
                return $sale->sale_date->format('Y-m-d');
            })->map(function($daySales) {
                return [
                    'count' => $daySales->count(),
                    'total' => $daySales->sum('total_amount'),
                    'average' => $daySales->avg('total_amount'),
                ];
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'period' => [
                        'from' => $dateFrom,
                        'to' => $dateTo
                    ],
                    'summary' => [
                        'total_sales' => $sales->count(),
                        'total_revenue' => $sales->sum('total_amount'),
                        'average_sale' => $sales->avg('total_amount'),
                        'total_items_sold' => $sales->sum(function($sale) {
                            return $sale->items->sum('quantity');
                        }),
                    ],
                    'daily_breakdown' => $dailySales,
                    'payment_methods' => [
                        'cash' => $sales->where('payment_method', 'cash')->sum('total_amount'),
                        'card' => $sales->where('payment_method', 'card')->sum('total_amount'),
                        'transfer' => $sales->where('payment_method', 'transfer')->sum('total_amount'),
                        'credit' => $sales->where('payment_method', 'credit')->sum('total_amount'),
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في جلب ملخص المبيعات'
            ], 500);
        }
    }

    /**
     * الحصول على أفضل المنتجات مبيعاً
     */
    private function getTopSellingProducts($companyId, $branchId, $dateFilter)
    {
        $query = SaleItem::select('product_id', 'product_name')
            ->selectRaw('SUM(quantity) as total_quantity')
            ->selectRaw('SUM(total_amount) as total_revenue')
            ->join('sales', 'sale_items.sale_id', '=', 'sales.id')
            ->where('sales.company_id', $companyId)
            ->where('sales.status', 'completed')
            ->whereBetween('sales.sale_date', $dateFilter);

        if ($branchId) {
            $query->where('sales.branch_id', $branchId);
        }

        return $query->groupBy('product_id', 'product_name')
            ->orderByDesc('total_quantity')
            ->limit(5)
            ->get();
    }

    /**
     * الحصول على المبيعات حسب الساعة
     */
    private function getHourlySales($sales)
    {
        return $sales->groupBy(function($sale) {
            return $sale->sale_date->format('H');
        })->map(function($hourlySales, $hour) {
            return [
                'hour' => $hour . ':00',
                'count' => $hourlySales->count(),
                'total' => $hourlySales->sum('total_amount'),
            ];
        })->sortKeys();
    }
}
