import React from 'react';
import {
  CurrencyDollarIcon,
  ShoppingCartIcon,
  UsersIcon,
  CubeIcon,
  TrendingUpIcon,
  TrendingDownIcon,
} from '@heroicons/react/24/outline';
import { useTheme } from '../contexts/ThemeContext';
import { useDashboardStats } from '../hooks/useApi';

const Dashboard: React.FC = () => {
  const { isRTL } = useTheme();
  const { data: stats, loading, error } = useDashboardStats({ period: 'today' });

  const statsCards = [
    {
      name: isRTL ? 'إجمالي المبيعات اليوم' : "Today's Sales",
      value: stats?.total_revenue || 0,
      change: '+12%',
      changeType: 'increase',
      icon: CurrencyDollarIcon,
      color: 'bg-green-500',
    },
    {
      name: isRTL ? 'عدد الفواتير' : 'Total Orders',
      value: stats?.total_sales || 0,
      change: '+8%',
      changeType: 'increase',
      icon: ShoppingCartIcon,
      color: 'bg-blue-500',
    },
    {
      name: isRTL ? 'العملاء الجدد' : 'New Customers',
      value: 24,
      change: '+3%',
      changeType: 'increase',
      icon: UsersIcon,
      color: 'bg-purple-500',
    },
    {
      name: isRTL ? 'المنتجات منخفضة المخزون' : 'Low Stock Items',
      value: 12,
      change: '-2%',
      changeType: 'decrease',
      icon: CubeIcon,
      color: 'bg-orange-500',
    },
  ];

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(isRTL ? 'ar-SA' : 'en-US', {
      style: 'currency',
      currency: 'SAR',
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="card p-6 animate-pulse">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4"></div>
              <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          {isRTL ? 'لوحة التحكم' : 'Dashboard'}
        </h1>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          {isRTL ? 'نظرة عامة على أداء متجرك اليوم' : 'Overview of your store performance today'}
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {statsCards.map((stat, index) => (
          <div key={index} className="card p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className={`${stat.color} p-3 rounded-lg`}>
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
              </div>
              <div className={`${isRTL ? 'mr-4' : 'ml-4'} flex-1`}>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                  {stat.name}
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {stat.name.includes(isRTL ? 'المبيعات' : 'Sales') 
                    ? formatCurrency(stat.value) 
                    : stat.value.toLocaleString()}
                </p>
                <div className="flex items-center mt-1">
                  {stat.changeType === 'increase' ? (
                    <TrendingUpIcon className="h-4 w-4 text-green-500" />
                  ) : (
                    <TrendingDownIcon className="h-4 w-4 text-red-500" />
                  )}
                  <span className={`text-sm font-medium ${
                    stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                  } ${isRTL ? 'mr-1' : 'ml-1'}`}>
                    {stat.change}
                  </span>
                  <span className="text-sm text-gray-500 dark:text-gray-400 ml-1">
                    {isRTL ? 'من الأمس' : 'from yesterday'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Charts and Tables Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sales Chart */}
        <div className="card p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            {isRTL ? 'المبيعات خلال الأسبوع' : 'Sales This Week'}
          </h3>
          <div className="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded-lg">
            <p className="text-gray-500 dark:text-gray-400">
              {isRTL ? 'الرسم البياني سيتم إضافته قريباً' : 'Chart will be added soon'}
            </p>
          </div>
        </div>

        {/* Top Products */}
        <div className="card p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            {isRTL ? 'أفضل المنتجات مبيعاً' : 'Top Selling Products'}
          </h3>
          <div className="space-y-3">
            {stats?.top_selling_products?.slice(0, 5).map((product: any, index: number) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="flex-shrink-0 h-10 w-10 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                    <span className="text-sm font-medium text-gray-600 dark:text-gray-300">
                      {index + 1}
                    </span>
                  </div>
                  <div className={`${isRTL ? 'mr-3' : 'ml-3'}`}>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {product.product_name}
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {product.total_quantity} {isRTL ? 'قطعة مباعة' : 'sold'}
                    </p>
                  </div>
                </div>
                <div className="text-sm font-medium text-gray-900 dark:text-white">
                  {formatCurrency(product.total_revenue)}
                </div>
              </div>
            )) || (
              <div className="text-center py-8">
                <p className="text-gray-500 dark:text-gray-400">
                  {isRTL ? 'لا توجد بيانات متاحة' : 'No data available'}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Recent Sales */}
      <div className="card p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            {isRTL ? 'المبيعات الأخيرة' : 'Recent Sales'}
          </h3>
          <a
            href="/sales"
            className="text-sm text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300"
          >
            {isRTL ? 'عرض الكل' : 'View all'}
          </a>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {isRTL ? 'رقم الفاتورة' : 'Invoice'}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {isRTL ? 'العميل' : 'Customer'}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {isRTL ? 'المبلغ' : 'Amount'}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {isRTL ? 'الحالة' : 'Status'}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {isRTL ? 'التاريخ' : 'Date'}
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
              {/* Sample data - replace with real data */}
              {[...Array(5)].map((_, index) => (
                <tr key={index}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    #INV-{1000 + index}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {isRTL ? 'عميل نقدي' : 'Walk-in Customer'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {formatCurrency(Math.random() * 1000 + 100)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                      {isRTL ? 'مكتملة' : 'Completed'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {new Date().toLocaleDateString(isRTL ? 'ar-SA' : 'en-US')}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
