<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->text('address')->nullable();
            $table->string('city')->nullable();
            $table->string('tax_number')->nullable(); // الرقم الضريبي للعميل
            $table->enum('type', ['individual', 'company'])->default('individual');
            $table->decimal('credit_limit', 10, 2)->default(0); // حد الائتمان
            $table->decimal('current_balance', 10, 2)->default(0); // الرصيد الحالي
            $table->date('birth_date')->nullable();
            $table->enum('gender', ['male', 'female'])->nullable();
            $table->json('preferences')->nullable(); // تفضيلات العميل
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_purchase_at')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['company_id', 'is_active']);
            $table->index(['phone', 'company_id']);
            $table->index(['email', 'company_id']);
            $table->index('tax_number');
            $table->fullText(['name', 'email', 'phone']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customers');
    }
};
