<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('companies', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('name_en')->nullable();
            $table->string('commercial_register')->unique();
            $table->string('tax_number')->unique();
            $table->string('email')->unique();
            $table->string('phone');
            $table->text('address');
            $table->text('address_en')->nullable();
            $table->string('city');
            $table->string('country')->default('SA');
            $table->string('logo')->nullable();
            $table->enum('status', ['active', 'inactive', 'suspended'])->default('active');
            $table->json('settings')->nullable(); // إعدادات خاصة بالشركة
            $table->decimal('subscription_price', 10, 2)->default(99.00);
            $table->integer('max_branches')->default(1);
            $table->integer('current_branches')->default(0);
            $table->timestamp('trial_ends_at')->nullable();
            $table->timestamp('subscription_ends_at')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['status', 'subscription_ends_at']);
            $table->index('commercial_register');
            $table->index('tax_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('companies');
    }
};
