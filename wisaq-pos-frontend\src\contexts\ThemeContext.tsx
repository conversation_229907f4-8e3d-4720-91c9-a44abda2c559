import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { ThemeConfig } from '../types';

interface ThemeContextType {
  theme: ThemeConfig;
  toggleMode: () => void;
  setLanguage: (language: 'ar' | 'en') => void;
  setPrimaryColor: (color: string) => void;
  isRTL: boolean;
  isDark: boolean;
}

const defaultTheme: ThemeConfig = {
  mode: 'light',
  primaryColor: '#3B82F6',
  language: 'ar',
  direction: 'rtl',
};

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [theme, setTheme] = useState<ThemeConfig>(defaultTheme);

  // Load theme from localStorage on mount
  useEffect(() => {
    const savedTheme = localStorage.getItem('wisaq_theme');
    if (savedTheme) {
      try {
        const parsedTheme = JSON.parse(savedTheme);
        setTheme({ ...defaultTheme, ...parsedTheme });
      } catch (error) {
        console.error('Error parsing saved theme:', error);
      }
    }
  }, []);

  // Apply theme changes to document
  useEffect(() => {
    applyTheme(theme);
    localStorage.setItem('wisaq_theme', JSON.stringify(theme));
  }, [theme]);

  const applyTheme = (themeConfig: ThemeConfig) => {
    const { mode, language, direction, primaryColor } = themeConfig;

    // Apply dark/light mode
    if (mode === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }

    // Apply language and direction
    document.documentElement.lang = language;
    document.documentElement.dir = direction;
    document.body.className = `font-${language === 'ar' ? 'arabic' : 'english'}`;

    // Apply primary color
    document.documentElement.style.setProperty('--primary-color', primaryColor);
    
    // Update CSS custom properties for primary color variations
    const primaryRGB = hexToRgb(primaryColor);
    if (primaryRGB) {
      document.documentElement.style.setProperty('--primary-rgb', `${primaryRGB.r}, ${primaryRGB.g}, ${primaryRGB.b}`);
    }
  };

  const hexToRgb = (hex: string) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  };

  const toggleMode = () => {
    setTheme(prev => ({
      ...prev,
      mode: prev.mode === 'light' ? 'dark' : 'light'
    }));
  };

  const setLanguage = (language: 'ar' | 'en') => {
    setTheme(prev => ({
      ...prev,
      language,
      direction: language === 'ar' ? 'rtl' : 'ltr'
    }));
  };

  const setPrimaryColor = (color: string) => {
    setTheme(prev => ({
      ...prev,
      primaryColor: color
    }));
  };

  const isRTL = theme.direction === 'rtl';
  const isDark = theme.mode === 'dark';

  const value: ThemeContextType = {
    theme,
    toggleMode,
    setLanguage,
    setPrimaryColor,
    isRTL,
    isDark,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export default ThemeContext;
