import React from 'react';
import { NavLink } from 'react-router-dom';
import {
  HomeIcon,
  ShoppingCartIcon,
  CubeIcon,
  TagIcon,
  UsersIcon,
  TruckIcon,
  ChartBarIcon,
  CogIcon,
  BuildingStorefrontIcon,
  UserGroupIcon,
  DocumentTextIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';

interface SidebarProps {
  onClose: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ onClose }) => {
  const { user, hasPermission, isSuperAdmin } = useAuth();
  const { isRTL } = useTheme();

  const navigation = [
    {
      name: isRTL ? 'لوحة التحكم' : 'Dashboard',
      href: '/dashboard',
      icon: HomeIcon,
      permission: null,
    },
    {
      name: isRTL ? 'نقاط البيع' : 'POS',
      href: '/pos',
      icon: ShoppingCartIcon,
      permission: 'pos.access',
    },
    {
      name: isRTL ? 'المنتجات' : 'Products',
      href: '/products',
      icon: CubeIcon,
      permission: 'products.view',
    },
    {
      name: isRTL ? 'الفئات' : 'Categories',
      href: '/categories',
      icon: TagIcon,
      permission: 'categories.view',
    },
    {
      name: isRTL ? 'المبيعات' : 'Sales',
      href: '/sales',
      icon: DocumentTextIcon,
      permission: 'sales.view',
    },
    {
      name: isRTL ? 'العملاء' : 'Customers',
      href: '/customers',
      icon: UsersIcon,
      permission: 'customers.view',
    },
    {
      name: isRTL ? 'الموردين' : 'Suppliers',
      href: '/suppliers',
      icon: TruckIcon,
      permission: 'suppliers.view',
    },
    {
      name: isRTL ? 'التقارير' : 'Reports',
      href: '/reports',
      icon: ChartBarIcon,
      permission: 'reports.view',
    },
  ];

  const managementNavigation = [
    {
      name: isRTL ? 'الفروع' : 'Branches',
      href: '/branches',
      icon: BuildingStorefrontIcon,
      permission: 'branches.view',
    },
    {
      name: isRTL ? 'المستخدمين' : 'Users',
      href: '/users',
      icon: UserGroupIcon,
      permission: 'users.view',
    },
    {
      name: isRTL ? 'الإعدادات' : 'Settings',
      href: '/settings',
      icon: CogIcon,
      permission: 'settings.view',
    },
  ];

  const adminNavigation = [
    {
      name: isRTL ? 'الشركات' : 'Companies',
      href: '/admin/companies',
      icon: BuildingStorefrontIcon,
      permission: 'companies.view',
    },
  ];

  const shouldShowNavItem = (permission: string | null) => {
    if (!permission) return true;
    return hasPermission(permission);
  };

  const NavItem = ({ item }: { item: any }) => (
    <NavLink
      to={item.href}
      onClick={onClose}
      className={({ isActive }) =>
        `sidebar-item ${isActive ? 'active' : ''} group flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200`
      }
    >
      <item.icon className="h-5 w-5 flex-shrink-0" />
      <span className={`${isRTL ? 'mr-3' : 'ml-3'}`}>{item.name}</span>
    </NavLink>
  );

  return (
    <div className="flex h-full flex-col bg-white dark:bg-gray-800 shadow-lg">
      {/* Header */}
      <div className="flex h-16 items-center justify-between px-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <img
              className="h-8 w-8"
              src="/logo.svg"
              alt="Wisaq POS"
            />
          </div>
          <div className={`${isRTL ? 'mr-3' : 'ml-3'}`}>
            <h1 className="text-xl font-bold text-gray-900 dark:text-white">
              Wisaq POS
            </h1>
            {user?.company && (
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {user.company.name}
              </p>
            )}
          </div>
        </div>
        
        {/* Close button for mobile */}
        <button
          onClick={onClose}
          className="lg:hidden rounded-md p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-500 dark:hover:bg-gray-700 dark:hover:text-gray-300"
        >
          <XMarkIcon className="h-6 w-6" />
        </button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-1 px-4 py-4 overflow-y-auto">
        {/* Main Navigation */}
        <div className="space-y-1">
          {navigation
            .filter(item => shouldShowNavItem(item.permission))
            .map((item) => (
              <NavItem key={item.name} item={item} />
            ))}
        </div>

        {/* Management Section */}
        {managementNavigation.some(item => shouldShowNavItem(item.permission)) && (
          <div className="pt-6">
            <h3 className="px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              {isRTL ? 'الإدارة' : 'Management'}
            </h3>
            <div className="mt-2 space-y-1">
              {managementNavigation
                .filter(item => shouldShowNavItem(item.permission))
                .map((item) => (
                  <NavItem key={item.name} item={item} />
                ))}
            </div>
          </div>
        )}

        {/* Super Admin Section */}
        {isSuperAdmin() && (
          <div className="pt-6">
            <h3 className="px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              {isRTL ? 'إدارة النظام' : 'System Admin'}
            </h3>
            <div className="mt-2 space-y-1">
              {adminNavigation.map((item) => (
                <NavItem key={item.name} item={item} />
              ))}
            </div>
          </div>
        )}
      </nav>

      {/* Footer */}
      <div className="border-t border-gray-200 dark:border-gray-700 p-4">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            {user?.avatar ? (
              <img
                className="h-8 w-8 rounded-full"
                src={user.avatar}
                alt={user.name}
              />
            ) : (
              <div className="h-8 w-8 rounded-full bg-primary-500 flex items-center justify-center">
                <span className="text-sm font-medium text-white">
                  {user?.name?.charAt(0)}
                </span>
              </div>
            )}
          </div>
          <div className={`${isRTL ? 'mr-3' : 'ml-3'} min-w-0 flex-1`}>
            <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
              {user?.name}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
              {user?.user_type === 'super_admin' && (isRTL ? 'مدير عام' : 'Super Admin')}
              {user?.user_type === 'admin' && (isRTL ? 'مدير' : 'Admin')}
              {user?.user_type === 'supervisor' && (isRTL ? 'مشرف' : 'Supervisor')}
              {user?.user_type === 'manager' && (isRTL ? 'مدير فرع' : 'Manager')}
              {user?.user_type === 'cashier' && (isRTL ? 'كاشير' : 'Cashier')}
              {user?.user_type === 'accountant' && (isRTL ? 'محاسب' : 'Accountant')}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
