import React, { useState } from 'react';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  UserIcon,
  BuildingOfficeIcon,
  PhoneIcon,
  EnvelopeIcon,
} from '@heroicons/react/24/outline';
import { useTheme } from '../contexts/ThemeContext';
import { useCustomers } from '../hooks/useApi';
import { Customer } from '../types';

const Customers: React.FC = () => {
  const { isRTL } = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);

  const { data: customers, loading, error, updateParams } = useCustomers({
    search: searchQuery,
    type: typeFilter,
    status: statusFilter,
  });

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    updateParams({ search: query });
  };

  const handleTypeFilter = (type: string) => {
    setTypeFilter(type);
    updateParams({ type });
  };

  const handleStatusFilter = (status: string) => {
    setStatusFilter(status);
    updateParams({ status });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(isRTL ? 'ar-SA' : 'en-US', {
      style: 'currency',
      currency: 'SAR',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(isRTL ? 'ar-SA' : 'en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getTypeIcon = (type: string) => {
    return type === 'company' ? BuildingOfficeIcon : UserIcon;
  };

  const getTypeText = (type: string) => {
    return type === 'company' 
      ? (isRTL ? 'شركة' : 'Company')
      : (isRTL ? 'فرد' : 'Individual');
  };

  const getStatusColor = (isActive: boolean) => {
    return isActive
      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
  };

  const getStatusText = (isActive: boolean) => {
    return isActive
      ? (isRTL ? 'نشط' : 'Active')
      : (isRTL ? 'غير نشط' : 'Inactive');
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-48 animate-pulse"></div>
          <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-32 animate-pulse"></div>
        </div>
        <div className="card p-6">
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            {isRTL ? 'إدارة العملاء' : 'Customer Management'}
          </h1>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {isRTL ? 'إدارة وتنظيم قاعدة بيانات العملاء' : 'Manage and organize your customer database'}
          </p>
        </div>
        <button
          onClick={() => setShowAddModal(true)}
          className="btn-primary flex items-center gap-2"
        >
          <PlusIcon className="h-5 w-5" />
          {isRTL ? 'إضافة عميل' : 'Add Customer'}
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="card p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="bg-blue-500 p-3 rounded-lg">
                <UserIcon className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className={`${isRTL ? 'mr-4' : 'ml-4'} flex-1`}>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {isRTL ? 'إجمالي العملاء' : 'Total Customers'}
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {customers?.length || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="bg-green-500 p-3 rounded-lg">
                <UserIcon className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className={`${isRTL ? 'mr-4' : 'ml-4'} flex-1`}>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {isRTL ? 'عملاء نشطين' : 'Active Customers'}
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {customers?.filter(c => c.is_active).length || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="bg-purple-500 p-3 rounded-lg">
                <BuildingOfficeIcon className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className={`${isRTL ? 'mr-4' : 'ml-4'} flex-1`}>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {isRTL ? 'عملاء شركات' : 'Corporate Customers'}
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {customers?.filter(c => c.type === 'company').length || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="bg-orange-500 p-3 rounded-lg">
                <UserIcon className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className={`${isRTL ? 'mr-4' : 'ml-4'} flex-1`}>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {isRTL ? 'عملاء أفراد' : 'Individual Customers'}
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {customers?.filter(c => c.type === 'individual').length || 0}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="card p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Search */}
          <div className="relative">
            <MagnifyingGlassIcon className={`absolute top-3 ${isRTL ? 'right-3' : 'left-3'} h-5 w-5 text-gray-400`} />
            <input
              type="text"
              placeholder={isRTL ? 'البحث في العملاء...' : 'Search customers...'}
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className={`input-field ${isRTL ? 'pr-10' : 'pl-10'}`}
            />
          </div>

          {/* Type Filter */}
          <select
            value={typeFilter}
            onChange={(e) => handleTypeFilter(e.target.value)}
            className="input-field"
          >
            <option value="">{isRTL ? 'جميع الأنواع' : 'All Types'}</option>
            <option value="individual">{isRTL ? 'فرد' : 'Individual'}</option>
            <option value="company">{isRTL ? 'شركة' : 'Company'}</option>
          </select>

          {/* Status Filter */}
          <select
            value={statusFilter}
            onChange={(e) => handleStatusFilter(e.target.value)}
            className="input-field"
          >
            <option value="">{isRTL ? 'جميع الحالات' : 'All Status'}</option>
            <option value="active">{isRTL ? 'نشط' : 'Active'}</option>
            <option value="inactive">{isRTL ? 'غير نشط' : 'Inactive'}</option>
          </select>

          {/* Advanced Filters */}
          <button className="btn-secondary flex items-center gap-2">
            <FunnelIcon className="h-5 w-5" />
            {isRTL ? 'فلاتر متقدمة' : 'Advanced Filters'}
          </button>
        </div>
      </div>

      {/* Customers List */}
      <div className="card overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {isRTL ? 'العميل' : 'Customer'}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {isRTL ? 'النوع' : 'Type'}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {isRTL ? 'معلومات الاتصال' : 'Contact Info'}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {isRTL ? 'الرصيد' : 'Balance'}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {isRTL ? 'آخر شراء' : 'Last Purchase'}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {isRTL ? 'الحالة' : 'Status'}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {isRTL ? 'الإجراءات' : 'Actions'}
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
              {customers?.map((customer: Customer) => {
                const TypeIcon = getTypeIcon(customer.type);
                return (
                  <tr key={customer.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                            <TypeIcon className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                          </div>
                        </div>
                        <div className={`${isRTL ? 'mr-4' : 'ml-4'}`}>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {customer.name}
                          </div>
                          {customer.tax_number && (
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              {isRTL ? 'الرقم الضريبي:' : 'Tax ID:'} {customer.tax_number}
                            </div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                        {getTypeText(customer.type)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {customer.phone && (
                          <div className="flex items-center gap-1 mb-1">
                            <PhoneIcon className="h-4 w-4 text-gray-400" />
                            {customer.phone}
                          </div>
                        )}
                        {customer.email && (
                          <div className="flex items-center gap-1">
                            <EnvelopeIcon className="h-4 w-4 text-gray-400" />
                            {customer.email}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm">
                        <div className={`font-medium ${
                          customer.current_balance >= 0 
                            ? 'text-green-600 dark:text-green-400' 
                            : 'text-red-600 dark:text-red-400'
                        }`}>
                          {formatCurrency(customer.current_balance)}
                        </div>
                        <div className="text-gray-500 dark:text-gray-400">
                          {isRTL ? 'حد الائتمان:' : 'Credit Limit:'} {formatCurrency(customer.credit_limit)}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {customer.last_purchase_at 
                        ? formatDate(customer.last_purchase_at)
                        : (isRTL ? 'لا يوجد' : 'None')
                      }
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(customer.is_active)}`}>
                        {getStatusText(customer.is_active)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => setSelectedCustomer(customer)}
                          className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                          title={isRTL ? 'عرض التفاصيل' : 'View Details'}
                        >
                          <EyeIcon className="h-4 w-4" />
                        </button>
                        <button
                          className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                          title={isRTL ? 'تعديل' : 'Edit'}
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button
                          className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                          title={isRTL ? 'حذف' : 'Delete'}
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>

        {/* Empty State */}
        {!loading && (!customers || customers.length === 0) && (
          <div className="text-center py-12">
            <UserIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
              {isRTL ? 'لا توجد عملاء' : 'No customers found'}
            </h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {isRTL ? 'ابدأ بإضافة عميل جديد' : 'Get started by adding a new customer'}
            </p>
            <div className="mt-6">
              <button
                onClick={() => setShowAddModal(true)}
                className="btn-primary flex items-center gap-2 mx-auto"
              >
                <PlusIcon className="h-5 w-5" />
                {isRTL ? 'إضافة عميل' : 'Add Customer'}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Customers;
