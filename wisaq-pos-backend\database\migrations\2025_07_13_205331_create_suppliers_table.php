<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('suppliers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->string('contact_person')->nullable(); // الشخص المسؤول
            $table->string('email')->nullable();
            $table->string('phone');
            $table->text('address')->nullable();
            $table->string('city')->nullable();
            $table->string('tax_number')->nullable(); // الرقم الضريبي للمورد
            $table->string('commercial_register')->nullable(); // السجل التجاري
            $table->decimal('credit_limit', 10, 2)->default(0); // حد الائتمان
            $table->decimal('current_balance', 10, 2)->default(0); // الرصيد الحالي
            $table->integer('payment_terms')->default(30); // شروط الدفع بالأيام
            $table->json('bank_details')->nullable(); // تفاصيل البنك
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_order_at')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['company_id', 'is_active']);
            $table->index(['phone', 'company_id']);
            $table->index(['email', 'company_id']);
            $table->index('tax_number');
            $table->fullText(['name', 'contact_person', 'email', 'phone']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('suppliers');
    }
};
