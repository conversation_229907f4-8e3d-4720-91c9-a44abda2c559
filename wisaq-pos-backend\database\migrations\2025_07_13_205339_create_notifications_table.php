<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('type'); // نوع التنبيه
            $table->string('title'); // عنوان التنبيه
            $table->text('message'); // رسالة التنبيه
            $table->json('data')->nullable(); // بيانات إضافية
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');
            $table->enum('channel', ['database', 'email', 'sms', 'push'])->default('database');
            $table->boolean('is_read')->default(false);
            $table->timestamp('read_at')->nullable();
            $table->timestamp('expires_at')->nullable(); // تاريخ انتهاء التنبيه
            $table->timestamps();

            // Indexes
            $table->index(['company_id', 'is_read', 'created_at']);
            $table->index(['user_id', 'is_read', 'created_at']);
            $table->index(['type', 'priority']);
            $table->index('expires_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notifications');
    }
};
