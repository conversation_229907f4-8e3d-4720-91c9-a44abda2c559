import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { User, LoginCredentials } from '../types';
import { apiService } from '../services/api';
import toast from 'react-hot-toast';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginCredentials) => Promise<boolean>;
  logout: () => void;
  refreshUser: () => Promise<void>;
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
  isSuperAdmin: () => boolean;
  isAdmin: () => boolean;
  isCashier: () => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user;

  // Initialize auth state
  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    try {
      const token = localStorage.getItem('wisaq_token');
      const savedUser = localStorage.getItem('wisaq_user');

      if (token && savedUser) {
        setUser(JSON.parse(savedUser));
        
        // Verify token and refresh user data
        try {
          await refreshUser();
        } catch (error) {
          // Token is invalid, clear auth data
          logout();
        }
      }
    } catch (error) {
      console.error('Error initializing auth:', error);
      logout();
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (credentials: LoginCredentials): Promise<boolean> => {
    try {
      setIsLoading(true);
      const response = await apiService.login(credentials);

      if (response.success && response.data) {
        setUser(response.data.user);
        localStorage.setItem('wisaq_user', JSON.stringify(response.data.user));
        toast.success('تم تسجيل الدخول بنجاح');
        return true;
      } else {
        toast.error(response.message || 'فشل في تسجيل الدخول');
        return false;
      }
    } catch (error: any) {
      const message = error.response?.data?.message || 'حدث خطأ في تسجيل الدخول';
      toast.error(message);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('wisaq_token');
    localStorage.removeItem('wisaq_user');
    apiService.logout();
    toast.success('تم تسجيل الخروج بنجاح');
  };

  const refreshUser = async () => {
    try {
      const response = await apiService.getMe();
      if (response.success && response.data) {
        setUser(response.data.user);
        localStorage.setItem('wisaq_user', JSON.stringify(response.data.user));
      }
    } catch (error) {
      throw error;
    }
  };

  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    if (user.user_type === 'super_admin') return true;
    return user.permissions.includes(permission);
  };

  const hasRole = (role: string): boolean => {
    if (!user) return false;
    return user.roles.includes(role) || user.user_type === role;
  };

  const isSuperAdmin = (): boolean => {
    return user?.user_type === 'super_admin';
  };

  const isAdmin = (): boolean => {
    return user?.user_type === 'admin' || isSuperAdmin();
  };

  const isCashier = (): boolean => {
    return user?.user_type === 'cashier';
  };

  const value: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    logout,
    refreshUser,
    hasPermission,
    hasRole,
    isSuperAdmin,
    isAdmin,
    isCashier,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
