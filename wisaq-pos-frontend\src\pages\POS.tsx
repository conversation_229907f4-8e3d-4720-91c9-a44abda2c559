import React, { useState, useEffect } from 'react';
import {
  MagnifyingGlassIcon,
  QrCodeIcon,
  ShoppingCartIcon,
  CreditCardIcon,
  BanknotesIcon,
  XMarkIcon,
  PlusIcon,
  MinusIcon,
  TrashIcon,
} from '@heroicons/react/24/outline';
import { useTheme } from '../contexts/ThemeContext';
import { Product, CartItem, Customer } from '../types';
import { apiService } from '../services/api';
import toast from 'react-hot-toast';

const POS: React.FC = () => {
  const { isRTL } = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [products, setProducts] = useState<Product[]>([]);
  const [cart, setCart] = useState<CartItem[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [showCustomerModal, setShowCustomerModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [loading, setLoading] = useState(false);

  // Cart calculations
  const cartSubtotal = cart.reduce((sum, item) => sum + item.total, 0);
  const cartTax = cartSubtotal * 0.15; // 15% VAT
  const cartTotal = cartSubtotal + cartTax;

  // Search products
  const searchProducts = async (query: string) => {
    if (!query.trim()) {
      setProducts([]);
      return;
    }

    try {
      setLoading(true);
      const response = await apiService.searchProducts(query);
      if (response.success) {
        setProducts(response.data || []);
      }
    } catch (error) {
      console.error('Error searching products:', error);
    } finally {
      setLoading(false);
    }
  };

  // Search by barcode
  const searchByBarcode = async (barcode: string) => {
    try {
      const response = await apiService.findProductByBarcode(barcode);
      if (response.success && response.data) {
        addToCart(response.data);
        setSearchQuery('');
        setProducts([]);
      } else {
        toast.error(isRTL ? 'المنتج غير موجود' : 'Product not found');
      }
    } catch (error) {
      toast.error(isRTL ? 'خطأ في البحث بالباركود' : 'Error searching by barcode');
    }
  };

  // Add product to cart
  const addToCart = (product: Product) => {
    const existingItem = cart.find(item => item.product.id === product.id);
    
    if (existingItem) {
      updateCartItemQuantity(product.id, existingItem.quantity + 1);
    } else {
      const newItem: CartItem = {
        product,
        quantity: 1,
        unit_price: product.discount_price || product.selling_price,
        total: product.discount_price || product.selling_price,
      };
      setCart([...cart, newItem]);
    }
    
    toast.success(isRTL ? 'تم إضافة المنتج للسلة' : 'Product added to cart');
  };

  // Update cart item quantity
  const updateCartItemQuantity = (productId: number, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeFromCart(productId);
      return;
    }

    setCart(cart.map(item => 
      item.product.id === productId 
        ? { ...item, quantity: newQuantity, total: item.unit_price * newQuantity }
        : item
    ));
  };

  // Remove from cart
  const removeFromCart = (productId: number) => {
    setCart(cart.filter(item => item.product.id !== productId));
  };

  // Clear cart
  const clearCart = () => {
    setCart([]);
    setSelectedCustomer(null);
  };

  // Process sale
  const processSale = async (paymentMethod: string, paidAmount: number) => {
    try {
      setLoading(true);
      
      const saleData = {
        customer_id: selectedCustomer?.id,
        items: cart.map(item => ({
          product_id: item.product.id,
          quantity: item.quantity,
          unit_price: item.unit_price,
        })),
        payment_method: paymentMethod,
        payment_details: { paid_amount: paidAmount },
      };

      const createResponse = await apiService.createSale(saleData);
      
      if (createResponse.success) {
        const paymentData = {
          sale_id: createResponse.data.id,
          paid_amount: paidAmount,
          payment_method: paymentMethod,
        };

        const paymentResponse = await apiService.processPayment(paymentData);
        
        if (paymentResponse.success) {
          toast.success(isRTL ? 'تم إتمام البيع بنجاح' : 'Sale completed successfully');
          clearCart();
          setShowPaymentModal(false);
          
          // Print receipt
          if (window.confirm(isRTL ? 'هل تريد طباعة الفاتورة؟' : 'Do you want to print the receipt?')) {
            await apiService.printInvoice(paymentResponse.data.id);
          }
        }
      }
    } catch (error) {
      toast.error(isRTL ? 'خطأ في معالجة البيع' : 'Error processing sale');
    } finally {
      setLoading(false);
    }
  };

  // Handle search input
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchQuery.trim()) {
        // Check if it's a barcode (numbers only)
        if (/^\d+$/.test(searchQuery.trim())) {
          searchByBarcode(searchQuery.trim());
        } else {
          searchProducts(searchQuery);
        }
      } else {
        setProducts([]);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchQuery]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(isRTL ? 'ar-SA' : 'en-US', {
      style: 'currency',
      currency: 'SAR',
    }).format(amount);
  };

  return (
    <div className="h-screen flex bg-gray-50 dark:bg-gray-900">
      {/* Left Panel - Products */}
      <div className="flex-1 flex flex-col">
        {/* Search Header */}
        <div className="bg-white dark:bg-gray-800 p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-4">
            <div className="flex-1 relative">
              <MagnifyingGlassIcon className={`absolute top-3 ${isRTL ? 'right-3' : 'left-3'} h-5 w-5 text-gray-400`} />
              <input
                type="text"
                placeholder={isRTL ? 'البحث بالاسم أو الباركود...' : 'Search by name or barcode...'}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className={`input-field ${isRTL ? 'pr-10' : 'pl-10'} text-lg`}
                autoFocus
              />
            </div>
            <button className="btn-secondary p-3">
              <QrCodeIcon className="h-6 w-6" />
            </button>
          </div>
        </div>

        {/* Products Grid */}
        <div className="flex-1 overflow-y-auto p-4">
          {loading ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="card p-4 animate-pulse">
                  <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded mb-3"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
                </div>
              ))}
            </div>
          ) : products.length > 0 ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {products.map((product) => (
                <div
                  key={product.id}
                  onClick={() => addToCart(product)}
                  className="card p-4 cursor-pointer hover:shadow-lg transition-shadow duration-200 hover:bg-primary-50 dark:hover:bg-primary-900/20"
                >
                  <div className="h-32 bg-gray-100 dark:bg-gray-700 rounded mb-3 flex items-center justify-center">
                    {product.image ? (
                      <img
                        src={product.image}
                        alt={product.name}
                        className="w-full h-full object-cover rounded"
                      />
                    ) : (
                      <span className="text-gray-400 text-sm">
                        {isRTL ? 'لا توجد صورة' : 'No Image'}
                      </span>
                    )}
                  </div>
                  <h3 className="font-medium text-gray-900 dark:text-white text-sm mb-1 truncate">
                    {product.name}
                  </h3>
                  <p className="text-lg font-bold text-primary-600 dark:text-primary-400">
                    {formatCurrency(product.discount_price || product.selling_price)}
                  </p>
                  {product.track_quantity && (
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {isRTL ? 'متوفر:' : 'Stock:'} {product.quantity}
                    </p>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <MagnifyingGlassIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                  {isRTL ? 'ابحث عن المنتجات' : 'Search for products'}
                </h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  {isRTL ? 'استخدم شريط البحث أو امسح الباركود' : 'Use the search bar or scan a barcode'}
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Right Panel - Cart */}
      <div className="w-96 bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 flex flex-col">
        {/* Cart Header */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
              <ShoppingCartIcon className="h-5 w-5" />
              {isRTL ? 'سلة التسوق' : 'Shopping Cart'}
            </h2>
            {cart.length > 0 && (
              <button
                onClick={clearCart}
                className="text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            )}
          </div>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {cart.length} {isRTL ? 'عنصر' : 'items'}
          </p>
        </div>

        {/* Customer Selection */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <button
            onClick={() => setShowCustomerModal(true)}
            className="w-full text-left p-3 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            {selectedCustomer ? (
              <div>
                <p className="font-medium text-gray-900 dark:text-white">
                  {selectedCustomer.name}
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {selectedCustomer.phone}
                </p>
              </div>
            ) : (
              <p className="text-gray-500 dark:text-gray-400">
                {isRTL ? 'اختيار عميل (اختياري)' : 'Select customer (optional)'}
              </p>
            )}
          </button>
        </div>

        {/* Cart Items */}
        <div className="flex-1 overflow-y-auto">
          {cart.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <ShoppingCartIcon className="mx-auto h-12 w-12 text-gray-400" />
                <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                  {isRTL ? 'السلة فارغة' : 'Cart is empty'}
                </p>
              </div>
            </div>
          ) : (
            <div className="p-4 space-y-3">
              {cart.map((item) => (
                <div key={item.product.id} className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900 dark:text-white text-sm">
                      {item.product.name}
                    </h4>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {formatCurrency(item.unit_price)}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => updateCartItemQuantity(item.product.id, item.quantity - 1)}
                      className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                    >
                      <MinusIcon className="h-4 w-4" />
                    </button>
                    <span className="w-8 text-center text-sm font-medium text-gray-900 dark:text-white">
                      {item.quantity}
                    </span>
                    <button
                      onClick={() => updateCartItemQuantity(item.product.id, item.quantity + 1)}
                      className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                    >
                      <PlusIcon className="h-4 w-4" />
                    </button>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-gray-900 dark:text-white">
                      {formatCurrency(item.total)}
                    </p>
                    <button
                      onClick={() => removeFromCart(item.product.id)}
                      className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Cart Summary */}
        {cart.length > 0 && (
          <div className="p-4 border-t border-gray-200 dark:border-gray-700 space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-500 dark:text-gray-400">
                  {isRTL ? 'المجموع الفرعي:' : 'Subtotal:'}
                </span>
                <span className="text-gray-900 dark:text-white">
                  {formatCurrency(cartSubtotal)}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-500 dark:text-gray-400">
                  {isRTL ? 'الضريبة (15%):' : 'Tax (15%):'}
                </span>
                <span className="text-gray-900 dark:text-white">
                  {formatCurrency(cartTax)}
                </span>
              </div>
              <div className="flex justify-between text-lg font-bold border-t border-gray-200 dark:border-gray-700 pt-2">
                <span className="text-gray-900 dark:text-white">
                  {isRTL ? 'الإجمالي:' : 'Total:'}
                </span>
                <span className="text-primary-600 dark:text-primary-400">
                  {formatCurrency(cartTotal)}
                </span>
              </div>
            </div>

            <button
              onClick={() => setShowPaymentModal(true)}
              className="btn-primary w-full py-3 text-lg"
              disabled={loading}
            >
              {loading ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  {isRTL ? 'جاري المعالجة...' : 'Processing...'}
                </div>
              ) : (
                <>
                  <CreditCardIcon className="h-5 w-5 mr-2" />
                  {isRTL ? 'إتمام البيع' : 'Complete Sale'}
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default POS;
