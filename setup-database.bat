@echo off
title Wisaq POS Database Setup
color 0E

echo.
echo ========================================
echo    WISAQ POS DATABASE SETUP
echo ========================================
echo.

cd wisaq-pos-backend

echo [1/4] Creating database...
echo.
echo Please make sure MySQL is running and accessible.
echo.

mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS wisaq_pos CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
if %errorlevel% neq 0 (
    echo.
    echo ERROR: Failed to create database
    echo Please check your MySQL connection and try again
    echo.
    echo Manual steps:
    echo 1. Open MySQL command line or phpMyAdmin
    echo 2. Run: CREATE DATABASE wisaq_pos CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
    echo 3. Update the .env file with your database credentials
    echo.
    pause
    exit /b 1
)

echo.
echo [2/4] Running migrations...
php artisan migrate --force
if %errorlevel% neq 0 (
    echo ERROR: Failed to run migrations
    pause
    exit /b 1
)

echo.
echo [3/4] Seeding database with initial data...
php artisan db:seed --force
if %errorlevel% neq 0 (
    echo ERROR: Failed to seed database
    pause
    exit /b 1
)

echo.
echo [4/4] Clearing cache...
php artisan config:clear
php artisan cache:clear
php artisan route:clear

echo.
echo ========================================
echo    DATABASE SETUP COMPLETED
echo ========================================
echo.
echo Database: wisaq_pos
echo Default admin user created:
echo Email: <EMAIL>
echo Password: password
echo.
echo You can now start the system using start-wisaq-pos.bat
echo.
echo Press any key to continue...
pause > nul
