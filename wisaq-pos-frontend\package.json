{"name": "wisaq-pos-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "@tanstack/react-query": "^5.0.0", "@headlessui/react": "^2.0.0", "@heroicons/react": "^2.0.0", "@hookform/resolvers": "^3.0.0", "axios": "^1.6.0", "react-router-dom": "^6.20.0", "react-hook-form": "^7.48.0", "react-hot-toast": "^2.4.0", "yup": "^1.4.0", "i18next": "^23.7.0", "react-i18next": "^13.5.0", "i18next-browser-languagedetector": "^7.2.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}