@echo off
title Wisaq POS Production Deployment
color 0C

echo.
echo ========================================
echo    WISAQ POS PRODUCTION DEPLOYMENT
echo ========================================
echo.

echo WARNING: This will prepare the system for production deployment
echo Make sure you have configured all environment variables properly
echo.
set /p confirm="Continue? (y/N): "
if /i not "%confirm%"=="y" (
    echo Deployment cancelled.
    pause
    exit /b 0
)

echo.
echo [1/6] Optimizing Backend for production...
cd wisaq-pos-backend

echo Installing production dependencies...
composer install --no-dev --optimize-autoloader

echo Caching configuration...
php artisan config:cache

echo Caching routes...
php artisan route:cache

echo Caching views...
php artisan view:cache

echo.
echo [2/6] Building Frontend for production...
cd ../wisaq-pos-frontend

echo Building React application...
npm run build
if %errorlevel% neq 0 (
    echo ERROR: Failed to build frontend
    pause
    exit /b 1
)

echo.
echo [3/6] Running database migrations...
cd ../wisaq-pos-backend
php artisan migrate --force

echo.
echo [4/6] Optimizing application...
php artisan optimize

echo.
echo [5/6] Setting proper permissions...
echo Please ensure the following directories are writable by the web server:
echo - storage/
echo - bootstrap/cache/
echo - public/storage/

echo.
echo [6/6] Creating symbolic link for storage...
php artisan storage:link

echo.
echo ========================================
echo    PRODUCTION DEPLOYMENT COMPLETED
echo ========================================
echo.
echo Next steps for production:
echo 1. Upload files to your web server
echo 2. Point your domain to the 'public' directory
echo 3. Set up SSL certificate (HTTPS)
echo 4. Configure your web server (Apache/Nginx)
echo 5. Set up cron job for Laravel scheduler:
echo    * * * * * cd /path/to/wisaq-pos-backend && php artisan schedule:run >> /dev/null 2>&1
echo.
echo Important security notes:
echo - Change APP_KEY in production
echo - Set APP_DEBUG=false
echo - Use strong database passwords
echo - Configure proper file permissions
echo - Set up regular backups
echo.
echo Press any key to continue...
pause > nul
