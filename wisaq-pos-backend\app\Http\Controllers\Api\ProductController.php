<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ProductController extends Controller
{
    /**
     * عرض قائمة المنتجات
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;
            $perPage = $request->get('per_page', 15);

            $query = Product::with(['category', 'company'])
                ->inCompany($companyId);

            // فلترة حسب الفئة
            if ($request->has('category_id') && $request->category_id) {
                $query->where('category_id', $request->category_id);
            }

            // فلترة حسب الحالة
            if ($request->has('status')) {
                if ($request->status === 'active') {
                    $query->active();
                } elseif ($request->status === 'inactive') {
                    $query->where('is_active', false);
                }
            }

            // فلترة المنتجات المميزة
            if ($request->has('featured') && $request->featured) {
                $query->featured();
            }

            // فلترة المنتجات منخفضة المخزون
            if ($request->has('low_stock') && $request->low_stock) {
                $query->lowStock();
            }

            // فلترة المنتجات منتهية الصلاحية
            if ($request->has('expired') && $request->expired) {
                $query->expired();
            }

            // البحث
            if ($request->has('search') && $request->search) {
                $query->search($request->search);
            }

            // الترتيب
            $sortBy = $request->get('sort_by', 'created_at');
            $sortOrder = $request->get('sort_order', 'desc');
            $query->orderBy($sortBy, $sortOrder);

            $products = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => [
                    'products' => $products->items(),
                    'pagination' => [
                        'current_page' => $products->currentPage(),
                        'last_page' => $products->lastPage(),
                        'per_page' => $products->perPage(),
                        'total' => $products->total(),
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في جلب المنتجات'
            ], 500);
        }
    }

    /**
     * إنشاء منتج جديد
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'name_en' => 'nullable|string|max:255',
                'category_id' => 'required|exists:categories,id',
                'description' => 'nullable|string',
                'description_en' => 'nullable|string',
                'sku' => 'required|string|unique:products,sku',
                'barcode' => 'nullable|string|unique:products,barcode',
                'cost_price' => 'required|numeric|min:0',
                'selling_price' => 'required|numeric|min:0',
                'discount_price' => 'nullable|numeric|min:0',
                'tax_type' => 'required|in:inclusive,exclusive,none',
                'tax_rate' => 'required|numeric|min:0|max:100',
                'unit' => 'required|string|max:50',
                'track_quantity' => 'boolean',
                'quantity' => 'required_if:track_quantity,true|integer|min:0',
                'min_quantity' => 'required_if:track_quantity,true|integer|min:0',
                'max_quantity' => 'nullable|integer|min:1',
                'expiry_date' => 'nullable|date|after:today',
                'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
                'is_featured' => 'boolean',
                'attributes' => 'nullable|array',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صحيحة',
                    'errors' => $validator->errors()
                ], 422);
            }

            $companyId = Auth::user()->company_id;

            // التحقق من أن الفئة تنتمي للشركة
            $category = Category::where('id', $request->category_id)
                ->where('company_id', $companyId)
                ->first();

            if (!$category) {
                return response()->json([
                    'success' => false,
                    'message' => 'الفئة المحددة غير موجودة'
                ], 404);
            }

            $data = $request->all();
            $data['company_id'] = $companyId;

            // رفع الصورة
            if ($request->hasFile('image')) {
                $image = $request->file('image');
                $imageName = time() . '_' . Str::random(10) . '.' . $image->getClientOriginalExtension();
                $imagePath = $image->storeAs('products', $imageName, 'public');
                $data['image'] = $imagePath;
            }

            // إنشاء المنتج
            $product = Product::create($data);

            // تسجيل في سجل المخزون إذا كان يتتبع الكمية
            if ($product->track_quantity && $product->quantity > 0) {
                $product->updateQuantity(0, 'initial_stock', null, Auth::id(), Auth::user()->branch_id);
            }

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء المنتج بنجاح',
                'data' => $product->load(['category', 'company'])
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في إنشاء المنتج'
            ], 500);
        }
    }

    /**
     * عرض منتج محدد
     */
    public function show($id): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;

            $product = Product::with(['category', 'company', 'inventoryLogs' => function($query) {
                $query->latest()->limit(10);
            }])
                ->inCompany($companyId)
                ->find($id);

            if (!$product) {
                return response()->json([
                    'success' => false,
                    'message' => 'المنتج غير موجود'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $product
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في جلب بيانات المنتج'
            ], 500);
        }
    }

    /**
     * تحديث منتج
     */
    public function update(Request $request, $id): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;

            $product = Product::inCompany($companyId)->find($id);

            if (!$product) {
                return response()->json([
                    'success' => false,
                    'message' => 'المنتج غير موجود'
                ], 404);
            }

            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'name_en' => 'nullable|string|max:255',
                'category_id' => 'required|exists:categories,id',
                'description' => 'nullable|string',
                'description_en' => 'nullable|string',
                'sku' => 'required|string|unique:products,sku,' . $id,
                'barcode' => 'nullable|string|unique:products,barcode,' . $id,
                'cost_price' => 'required|numeric|min:0',
                'selling_price' => 'required|numeric|min:0',
                'discount_price' => 'nullable|numeric|min:0',
                'tax_type' => 'required|in:inclusive,exclusive,none',
                'tax_rate' => 'required|numeric|min:0|max:100',
                'unit' => 'required|string|max:50',
                'track_quantity' => 'boolean',
                'min_quantity' => 'required_if:track_quantity,true|integer|min:0',
                'max_quantity' => 'nullable|integer|min:1',
                'expiry_date' => 'nullable|date|after:today',
                'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
                'is_featured' => 'boolean',
                'attributes' => 'nullable|array',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صحيحة',
                    'errors' => $validator->errors()
                ], 422);
            }

            // التحقق من أن الفئة تنتمي للشركة
            $category = Category::where('id', $request->category_id)
                ->where('company_id', $companyId)
                ->first();

            if (!$category) {
                return response()->json([
                    'success' => false,
                    'message' => 'الفئة المحددة غير موجودة'
                ], 404);
            }

            $data = $request->except(['quantity']); // منع تحديث الكمية من هنا

            // رفع الصورة الجديدة
            if ($request->hasFile('image')) {
                // حذف الصورة القديمة
                if ($product->image) {
                    Storage::disk('public')->delete($product->image);
                }

                $image = $request->file('image');
                $imageName = time() . '_' . Str::random(10) . '.' . $image->getClientOriginalExtension();
                $imagePath = $image->storeAs('products', $imageName, 'public');
                $data['image'] = $imagePath;
            }

            $product->update($data);

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث المنتج بنجاح',
                'data' => $product->load(['category', 'company'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في تحديث المنتج'
            ], 500);
        }
    }

    /**
     * حذف منتج
     */
    public function destroy($id): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;

            $product = Product::inCompany($companyId)->find($id);

            if (!$product) {
                return response()->json([
                    'success' => false,
                    'message' => 'المنتج غير موجود'
                ], 404);
            }

            // التحقق من وجود مبيعات للمنتج
            if ($product->saleItems()->exists()) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا يمكن حذف المنتج لوجود مبيعات مرتبطة به'
                ], 400);
            }

            // حذف الصورة
            if ($product->image) {
                Storage::disk('public')->delete($product->image);
            }

            $product->delete();

            return response()->json([
                'success' => true,
                'message' => 'تم حذف المنتج بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في حذف المنتج'
            ], 500);
        }
    }

    /**
     * تبديل حالة المنتج (نشط/غير نشط)
     */
    public function toggleStatus($id): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;

            $product = Product::inCompany($companyId)->find($id);

            if (!$product) {
                return response()->json([
                    'success' => false,
                    'message' => 'المنتج غير موجود'
                ], 404);
            }

            $product->update(['is_active' => !$product->is_active]);

            return response()->json([
                'success' => true,
                'message' => $product->is_active ? 'تم تفعيل المنتج' : 'تم إلغاء تفعيل المنتج',
                'data' => $product
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في تغيير حالة المنتج'
            ], 500);
        }
    }

    /**
     * تبديل حالة المنتج المميز
     */
    public function toggleFeatured($id): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;

            $product = Product::inCompany($companyId)->find($id);

            if (!$product) {
                return response()->json([
                    'success' => false,
                    'message' => 'المنتج غير موجود'
                ], 404);
            }

            $product->update(['is_featured' => !$product->is_featured]);

            return response()->json([
                'success' => true,
                'message' => $product->is_featured ? 'تم إضافة المنتج للمميزة' : 'تم إزالة المنتج من المميزة',
                'data' => $product
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في تغيير حالة المنتج المميز'
            ], 500);
        }
    }

    /**
     * تحديث كمية المنتج
     */
    public function updateQuantity(Request $request, $id): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'quantity_change' => 'required|integer',
                'type' => 'required|in:purchase,adjustment,transfer,return,damage,expired',
                'notes' => 'nullable|string|max:500',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صحيحة',
                    'errors' => $validator->errors()
                ], 422);
            }

            $companyId = Auth::user()->company_id;

            $product = Product::inCompany($companyId)->find($id);

            if (!$product) {
                return response()->json([
                    'success' => false,
                    'message' => 'المنتج غير موجود'
                ], 404);
            }

            if (!$product->track_quantity) {
                return response()->json([
                    'success' => false,
                    'message' => 'هذا المنتج لا يتتبع الكمية'
                ], 400);
            }

            $oldQuantity = $product->quantity;
            $product->updateQuantity(
                $request->quantity_change,
                $request->type,
                null,
                Auth::id(),
                Auth::user()->branch_id
            );

            // إضافة الملاحظات إلى آخر سجل مخزون
            if ($request->notes) {
                $product->inventoryLogs()->latest()->first()->update([
                    'notes' => $request->notes
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث كمية المنتج بنجاح',
                'data' => [
                    'product' => $product->fresh(),
                    'old_quantity' => $oldQuantity,
                    'new_quantity' => $product->quantity,
                    'change' => $request->quantity_change
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في تحديث كمية المنتج'
            ], 500);
        }
    }

    /**
     * البحث في المنتجات
     */
    public function search($query): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;

            $products = Product::with(['category'])
                ->inCompany($companyId)
                ->active()
                ->search($query)
                ->limit(20)
                ->get();

            return response()->json([
                'success' => true,
                'data' => $products
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في البحث'
            ], 500);
        }
    }

    /**
     * البحث بالباركود
     */
    public function findByBarcode($barcode): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;

            $product = Product::with(['category'])
                ->inCompany($companyId)
                ->active()
                ->where('barcode', $barcode)
                ->first();

            if (!$product) {
                return response()->json([
                    'success' => false,
                    'message' => 'المنتج غير موجود'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $product
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في البحث بالباركود'
            ], 500);
        }
    }

    /**
     * المنتجات منخفضة المخزون
     */
    public function lowStock(): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;

            $products = Product::with(['category'])
                ->inCompany($companyId)
                ->active()
                ->lowStock()
                ->get();

            return response()->json([
                'success' => true,
                'data' => $products,
                'count' => $products->count()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في جلب المنتجات منخفضة المخزون'
            ], 500);
        }
    }

    /**
     * المنتجات منتهية الصلاحية
     */
    public function expired(): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;

            $products = Product::with(['category'])
                ->inCompany($companyId)
                ->active()
                ->expired()
                ->get();

            return response()->json([
                'success' => true,
                'data' => $products,
                'count' => $products->count()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في جلب المنتجات منتهية الصلاحية'
            ], 500);
        }
    }

    /**
     * المنتجات قريبة الانتهاء
     */
    public function expiringSoon(Request $request): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;
            $days = $request->get('days', 30);

            $products = Product::with(['category'])
                ->inCompany($companyId)
                ->active()
                ->expiringSoon($days)
                ->get();

            return response()->json([
                'success' => true,
                'data' => $products,
                'count' => $products->count(),
                'days' => $days
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في جلب المنتجات قريبة الانتهاء'
            ], 500);
        }
    }

    /**
     * أفضل المنتجات مبيعاً
     */
    public function topProducts(Request $request): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;
            $limit = $request->get('limit', 10);
            $period = $request->get('period', 'month'); // day, week, month, year

            $dateFilter = match($period) {
                'day' => now()->startOfDay(),
                'week' => now()->startOfWeek(),
                'month' => now()->startOfMonth(),
                'year' => now()->startOfYear(),
                default => now()->startOfMonth()
            };

            $products = Product::with(['category'])
                ->inCompany($companyId)
                ->select('products.*')
                ->selectRaw('SUM(sale_items.quantity) as total_sold')
                ->selectRaw('SUM(sale_items.total_amount) as total_revenue')
                ->join('sale_items', 'products.id', '=', 'sale_items.product_id')
                ->join('sales', 'sale_items.sale_id', '=', 'sales.id')
                ->where('sales.status', 'completed')
                ->where('sales.sale_date', '>=', $dateFilter)
                ->groupBy('products.id')
                ->orderByDesc('total_sold')
                ->limit($limit)
                ->get();

            return response()->json([
                'success' => true,
                'data' => $products,
                'period' => $period
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في جلب أفضل المنتجات'
            ], 500);
        }
    }

    /**
     * تقرير المخزون
     */
    public function inventoryReport(Request $request): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;

            $products = Product::with(['category'])
                ->inCompany($companyId)
                ->active()
                ->get();

            $totalProducts = $products->count();
            $totalValue = $products->sum(function($product) {
                return $product->quantity * $product->cost_price;
            });

            $lowStockCount = $products->filter(function($product) {
                return $product->isLowStock();
            })->count();

            $outOfStockCount = $products->filter(function($product) {
                return $product->isOutOfStock();
            })->count();

            $expiredCount = $products->filter(function($product) {
                return $product->isExpired();
            })->count();

            return response()->json([
                'success' => true,
                'data' => [
                    'summary' => [
                        'total_products' => $totalProducts,
                        'total_inventory_value' => $totalValue,
                        'low_stock_count' => $lowStockCount,
                        'out_of_stock_count' => $outOfStockCount,
                        'expired_count' => $expiredCount,
                    ],
                    'products' => $products
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في إنشاء تقرير المخزون'
            ], 500);
        }
    }
}
